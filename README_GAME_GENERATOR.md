# 游戏引擎框架生成脚本使用文档

## 概述

本项目提供了两个游戏代码生成脚本，用于快速创建游戏引擎框架：

1. **generate_game.py** - 基础版本，生成标准的游戏框架
2. **generate_game_advanced.py** - 高级版本，支持多种游戏类型和自定义选项

## 功能特性

### 基础版本 (generate_game.py)
- 生成标准的游戏模块文件 (`modules/m{gameId}.go`)
- 生成HTTP游戏处理文件 (`http/games/s{gameId}.go`)
- 生成配置文件模板 (`configs/{gameId}.yaml`)
- 自动分析现有代码模式
- 提供完整的使用说明

### 高级版本 (generate_game_advanced.py)
- 支持多种游戏类型：
  - **slot** - 传统老虎机（支付线模式）
  - **ways** - Ways玩法（连续符号计算）
  - **cascade** - 级联消除（符号消除和下落）
  - **poker** - 扑克游戏（牌型判断）
- 交互式配置模式
- 自定义游戏参数（行数、列数、线数等）
- 针对不同游戏类型的专用模板

## 使用方法

### 基础版本使用

```bash
# 基本用法
python generate_game.py <gameId>

# 示例
python generate_game.py 10000999
```

### 高级版本使用

```bash
# 基本用法
python generate_game_advanced.py <gameId> [options]

# 生成传统老虎机游戏
python generate_game_advanced.py 10000999 --type slot --lines 25

# 生成Ways玩法游戏
python generate_game_advanced.py 10000888 --type ways --rows 6 --columns 5

# 生成级联消除游戏
python generate_game_advanced.py 10000777 --type cascade --rows 4 --columns 5

# 交互式配置模式
python generate_game_advanced.py --interactive
```

### 命令行选项

| 选项 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `game_id` | 字符串 | 必需 | 游戏ID（数字） |
| `--type` | 选择 | slot | 游戏类型 (slot/ways/cascade/poker) |
| `--lines` | 整数 | 20 | 线数或Ways数 |
| `--rows` | 整数 | 4 | 网格行数 |
| `--columns` | 整数 | 5 | 网格列数 |
| `--max-payout` | 整数 | 250000 | 最大奖金 |
| `--wild-icon` | 整数 | 9 | Wild图标ID |
| `--scatter-icon` | 整数 | 10 | Scatter图标ID |
| `--interactive` | 标志 | - | 启用交互式配置 |

## 生成的文件结构

```
项目根目录/
├── modules/
│   └── m{gameId}.go          # 游戏模块文件
├── http/games/
│   └── s{gameId}.go          # HTTP游戏处理文件
└── configs/
    └── {gameId}.yaml         # 配置文件
```

## 游戏类型说明

### 1. Slot (传统老虎机)
- 使用预定义的支付线模式
- 符号必须在支付线上连续出现
- 支持多条支付线同时中奖
- 适合传统的老虎机游戏

**特点：**
- 固定的支付线路径
- 连线检查基于预设模式
- 支持复杂的支付线组合

### 2. Ways (Ways玩法)
- 符号从左到右连续出现即可中奖
- Ways数 = 各列中奖符号数量相乘
- 不依赖固定的支付线
- 适合现代视频老虎机

**特点：**
- 更灵活的中奖方式
- 更高的中奖频率
- Ways数计算复杂

### 3. Cascade (级联消除)
- 中奖符号消除后上方符号下落
- 支持连续级联和乘数递增
- 类似消消乐的玩法
- 适合休闲类游戏

**特点：**
- 动态的游戏过程
- 连续中奖机会
- 乘数递增机制

### 4. Poker (扑克游戏)
- 基于扑克牌型的游戏
- 支持换牌功能
- 牌型判断和奖金计算
- 适合卡牌类游戏

**特点：**
- 策略性玩法
- 牌型组合判断
- 换牌决策机制

## 配置文件说明

生成的YAML配置文件包含以下主要部分：

```yaml
GameID: 10000999           # 游戏ID
RuleID: 1                  # 规则ID
Row: 4                     # 网格行数
Column: 5                  # 网格列数
MaxPayout: 250000          # 最大奖金

# 支付表配置
PayoutTable:
  - [8, 3, 50]    # [图标ID, 连线数, 倍率]
  - [8, 4, 200]
  - [8, 5, 500]

# 图标权重配置
IconWeight:
  1: 150          # 图标1的出现权重
  2: 150          # 图标2的出现权重
  # ...

WildIcon: 9       # Wild图标ID
ScatterIcon: 10   # Scatter图标ID
```

## 开发流程

### 1. 生成游戏框架
```bash
python generate_game_advanced.py 10000999 --type ways
```

### 2. 修改配置文件
编辑 `configs/10000999.yaml`，调整：
- 支付表倍率
- 图标权重
- 特殊功能配置

### 3. 实现游戏逻辑
在生成的模块文件中完善：
- `Spin()` 方法 - 主要游戏逻辑
- `calculateWin()` 方法 - 中奖计算
- 特殊功能实现

### 4. 生成游戏数据
```bash
cd bin
go run ../cmd/generate/main.go 10000999
```

### 5. 生成支付表
```bash
cd bin
go run ../cmd/paytab/main.go 10000999 0
```

### 6. 测试游戏
```bash
go test -bench=BenchmarkSpin -v ./test -args 10000999
```

## 注意事项

1. **游戏ID格式**：必须是纯数字，建议使用8位数字
2. **文件覆盖**：如果游戏ID已存在，脚本会询问是否覆盖
3. **代码完善**：生成的代码包含TODO注释，需要根据具体需求实现
4. **配置调整**：支付表和权重需要根据游戏设计进行调整
5. **测试验证**：生成后务必进行编译和运行测试

## 故障排除

### 常见问题

1. **权限错误**：确保对目标目录有写入权限
2. **路径错误**：确认工作目录正确
3. **格式错误**：检查游戏ID是否为纯数字
4. **依赖缺失**：确保Python环境正确

### 错误处理

脚本包含完整的错误处理机制：
- 输入验证
- 文件操作异常处理
- 详细的错误信息提示

## 扩展开发

### 添加新游戏类型

1. 在 `GameTemplateManager` 中添加新的模板方法
2. 更新 `templates` 字典
3. 添加相应的配置选项
4. 更新文档说明

### 自定义模板

可以创建自定义模板文件，通过 `--template` 选项使用：
```bash
python generate_game_advanced.py 10000999 --template my_template.json
```

## 技术支持

如有问题或建议，请：
1. 检查本文档的故障排除部分
2. 查看生成的代码注释
3. 参考现有游戏实现示例
4. 联系开发团队获取支持
