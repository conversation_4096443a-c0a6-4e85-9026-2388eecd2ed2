package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"path/filepath"
	"strings"
)

// GameType 游戏类型枚举
type GameType string

const (
	GameTypeSlot    GameType = "slot"    // 传统老虎机
	GameTypeWays    GameType = "ways"    // Ways玩法
	GameTypeCascade GameType = "cascade" // 级联消除
	GameTypeLine    GameType = "line"    // 连线玩法
)

// GameConfig 游戏配置结构
type GameConfig struct {
	GameID      int32    // 游戏ID
	GameType    GameType // 游戏类型
	Row         int32    // 行数
	Column      int32    // 列数
	Lines       int32    // 线数
	MaxPayout   int32    // 最大奖金
	Interactive bool     // 是否交互式配置
	Force       bool     // 是否强制覆盖
	Validate    bool     // 是否验证代码
}

// Generator 代码生成器
type Generator struct {
	config    GameConfig
	templates *TemplateManager
}

func main() {
	var config GameConfig
	var gameTypeStr string
	var gameID, row, column, lines, maxPayout int64

	// 解析命令行参数
	flag.Int64Var(&gameID, "id", 0, "游戏ID (必需)")
	flag.StringVar(&gameTypeStr, "type", "slot", "游戏类型 (slot/ways/cascade/line)")
	flag.Int64Var(&row, "row", 4, "网格行数")
	flag.Int64Var(&column, "column", 5, "网格列数")
	flag.Int64Var(&lines, "lines", 20, "线数")
	flag.Int64Var(&maxPayout, "max-payout", 250000, "最大奖金")
	flag.BoolVar(&config.Interactive, "interactive", false, "交互式配置模式")
	flag.BoolVar(&config.Force, "force", false, "强制覆盖现有文件")
	flag.BoolVar(&config.Validate, "validate", true, "验证生成的代码")

	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "游戏引擎框架生成器\n\n")
		fmt.Fprintf(os.Stderr, "用法: %s [选项]\n\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
		fmt.Fprintf(os.Stderr, "\n示例:\n")
		fmt.Fprintf(os.Stderr, "  %s -id=10000999 -type=slot -row=4 -column=5\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "  %s -id=10000999 -interactive\n", os.Args[0])
	}

	flag.Parse()

	// 转换参数类型
	config.GameID = int32(gameID)
	config.Row = int32(row)
	config.Column = int32(column)
	config.Lines = int32(lines)
	config.MaxPayout = int32(maxPayout)

	// 验证游戏ID
	if config.GameID == 0 {
		fmt.Fprintf(os.Stderr, "错误: 必须指定游戏ID\n\n")
		flag.Usage()
		os.Exit(1)
	}

	// 验证游戏类型
	config.GameType = GameType(strings.ToLower(gameTypeStr))
	if !isValidGameType(config.GameType) {
		fmt.Fprintf(os.Stderr, "错误: 无效的游戏类型 '%s'\n", gameTypeStr)
		fmt.Fprintf(os.Stderr, "支持的类型: slot, ways, cascade, line\n")
		os.Exit(1)
	}

	// 交互式配置
	if config.Interactive {
		configurer := NewInteractiveConfigurer()
		if err := configurer.ConfigureGame(&config); err != nil {
			log.Fatalf("交互式配置失败: %v", err)
		}
	}

	// 创建生成器
	generator, err := NewGenerator(config)
	if err != nil {
		log.Fatalf("创建生成器失败: %v", err)
	}

	// 执行生成
	if err := generator.Generate(); err != nil {
		log.Fatalf("生成失败: %v", err)
	}

	fmt.Printf("✅ 游戏 %d 生成成功!\n", config.GameID)
	fmt.Printf("📁 生成的文件:\n")
	fmt.Printf("   - modules/m%d.go\n", config.GameID)
	fmt.Printf("   - http/games/s%d.go\n", config.GameID)
	fmt.Printf("   - configs/%d.yaml\n", config.GameID)
	fmt.Printf("\n🔧 下一步操作:\n")
	fmt.Printf("   1. 运行: go run cmd/generate/main.go %d\n", config.GameID)
	fmt.Printf("   2. 运行: go run cmd/paytab/main.go %d\n", config.GameID)
	fmt.Printf("   3. 测试: make test id=%d\n", config.GameID)
}

// isValidGameType 检查游戏类型是否有效
func isValidGameType(gameType GameType) bool {
	switch gameType {
	case GameTypeSlot, GameTypeWays, GameTypeCascade, GameTypeLine:
		return true
	default:
		return false
	}
}

// NewGenerator 创建新的生成器
func NewGenerator(config GameConfig) (*Generator, error) {
	templates, err := NewTemplateManager()
	if err != nil {
		return nil, fmt.Errorf("创建模板管理器失败: %v", err)
	}

	return &Generator{
		config:    config,
		templates: templates,
	}, nil
}

// Generate 执行生成
func (g *Generator) Generate() error {
	// 检查文件冲突
	if err := g.checkConflicts(); err != nil {
		return err
	}

	// 生成模块文件
	if err := g.generateModule(); err != nil {
		return fmt.Errorf("生成模块文件失败: %v", err)
	}

	// 生成HTTP响应文件
	if err := g.generateHTTPGame(); err != nil {
		return fmt.Errorf("生成HTTP响应文件失败: %v", err)
	}

	// 生成配置文件
	if err := g.generateConfig(); err != nil {
		return fmt.Errorf("生成配置文件失败: %v", err)
	}

	// 验证生成的代码
	if g.config.Validate {
		if err := g.validateGenerated(); err != nil {
			return fmt.Errorf("代码验证失败: %v", err)
		}
	}

	return nil
}

// checkConflicts 检查文件冲突
func (g *Generator) checkConflicts() error {
	resolver := NewConflictResolver(".")

	// 验证文件权限
	if err := resolver.ValidatePermissions(g.config.GameID); err != nil {
		return fmt.Errorf("权限验证失败: %v", err)
	}

	// 检查文件冲突
	conflicts, err := resolver.CheckConflicts(g.config.GameID)
	if err != nil {
		return fmt.Errorf("冲突检查失败: %v", err)
	}

	// 解决冲突
	return resolver.ResolveConflicts(conflicts, g.config.Force, g.config.Interactive)
}

// ensureDir 确保目录存在
func ensureDir(path string) error {
	dir := filepath.Dir(path)
	return os.MkdirAll(dir, 0755)
}

// generateModule 生成模块文件
func (g *Generator) generateModule() error {
	filename := fmt.Sprintf("modules/m%d.go", g.config.GameID)
	if err := ensureDir(filename); err != nil {
		return err
	}

	content, err := g.templates.RenderModule(g.config)
	if err != nil {
		return err
	}

	return os.WriteFile(filename, []byte(content), 0644)
}

// generateHTTPGame 生成HTTP响应文件
func (g *Generator) generateHTTPGame() error {
	filename := fmt.Sprintf("http/games/s%d.go", g.config.GameID)
	if err := ensureDir(filename); err != nil {
		return err
	}

	content, err := g.templates.RenderHTTPGame(g.config)
	if err != nil {
		return err
	}

	return os.WriteFile(filename, []byte(content), 0644)
}

// generateConfig 生成配置文件
func (g *Generator) generateConfig() error {
	filename := fmt.Sprintf("configs/%d.yaml", g.config.GameID)
	if err := ensureDir(filename); err != nil {
		return err
	}

	content, err := g.templates.RenderConfig(g.config)
	if err != nil {
		return err
	}

	return os.WriteFile(filename, []byte(content), 0644)
}

// validateGenerated 验证生成的代码
func (g *Generator) validateGenerated() error {
	fmt.Printf("🔍 验证生成的代码...\n")

	validator := NewCodeValidator(".")

	// 验证模块文件
	if err := validator.ValidateModule(g.config.GameID); err != nil {
		return fmt.Errorf("模块文件验证失败: %v", err)
	}

	// 验证HTTP响应文件
	if err := validator.ValidateHTTPGame(g.config.GameID); err != nil {
		return fmt.Errorf("HTTP响应文件验证失败: %v", err)
	}

	// 验证配置文件
	if err := validator.ValidateConfig(g.config.GameID); err != nil {
		return fmt.Errorf("配置文件验证失败: %v", err)
	}

	// 验证项目编译
	if err := validator.ValidateCompilation(); err != nil {
		return fmt.Errorf("项目编译验证失败: %v", err)
	}

	// 生成并运行测试
	if err := validator.GenerateTestFile(g.config.GameID); err != nil {
		fmt.Printf("⚠️  生成测试文件失败: %v\n", err)
	} else {
		if err := validator.RunTests(g.config.GameID); err != nil {
			fmt.Printf("⚠️  测试运行失败: %v\n", err)
		}
		// 清理测试文件
		validator.CleanupTestFiles(g.config.GameID)
	}

	fmt.Printf("✅ 代码验证完成\n")
	return nil
}
