package main

// Ways游戏HTTP模板
const waysHTTPTemplate = `package games

import (
	"encoding/json"
	"igameCommon/basic"
)

type S{{.GameID}} struct {
	Pays   int32      // 总赔率
	Rounds []P{{.GameID}} // 每轮数据
}

type P{{.GameID}} struct {
	Pay   int32        // 赔率
	Pages [][]int32    // 每轮中的每一页
}

func (s *S{{.GameID}}) Payout() int32 {
	return s.Pays
}

func (s *S{{.GameID}}) RoundPayouts() []int32 {
	return []int32{s.Payout()}
}

func (s *S{{.GameID}}) Data(ctx basic.SpinContext) string {
	b, _ := json.Marshal(s.parseDetail())
	return string(b)
}

func (s *S{{.GameID}}) RoundData(index int32, ctx basic.SpinContext) string {
	return s.Data(ctx)
}

func (s *S{{.GameID}}) Tags() []string {
	return []string{basic.EnumSpinTag.NORM}
}

func (s *S{{.GameID}}) Exception(code int32) string {
	return "{}"
}

func (s *S{{.GameID}}) parseDetail() map[string]interface{} {
	return map[string]interface{}{
		"pays":   s.Pays,
		"rounds": s.Rounds,
	}
}
`

// Ways游戏配置模板
const waysConfigTemplate = `# 游戏 {{.GameID}} 配置文件
# 游戏类型: {{.GameType}} (Ways玩法)

Row: {{.Row}}
Column: {{.Column}}
MaxPayout: {{.MaxPayout}}

# Wild图标配置
WildIcon: 9

# 赔率表配置 (格式: [符号ID, 连线数, 赔率*100])
PayoutTable:
  # 发 (8)
  - [8, 3, 15]    # 3连：15x
  - [8, 4, 60]    # 4连：60x
  - [8, 5, 100]   # 5连：100x
  # 中 (7)
  - [7, 3, 10]    # 3连：10x
  - [7, 4, 40]    # 4连：40x
  - [7, 5, 80]    # 5连：80x
  # 白板 (6)
  - [6, 3, 8]     # 3连：8x
  - [6, 4, 20]    # 4连：20x
  - [6, 5, 60]    # 5连：60x
  # 八万 (5)
  - [5, 3, 6]     # 3连：6x
  - [5, 4, 15]    # 4连：15x
  - [5, 5, 40]    # 5连：40x
  # 五筒 (4)
  - [4, 3, 4]     # 3连：4x
  - [4, 4, 10]    # 4连：10x
  - [4, 5, 20]    # 5连：20x
  # 五条 (3)
  - [3, 3, 4]     # 3连：4x
  - [3, 4, 10]    # 4连：10x
  - [3, 5, 20]    # 5连：20x
  # 二筒 (2)
  - [2, 3, 2]     # 3连：2x
  - [2, 4, 5]     # 4连：5x
  - [2, 5, 10]    # 5连：10x
  # 二条 (1)
  - [1, 3, 2]     # 3连：2x
  - [1, 4, 5]     # 4连：5x
  - [1, 5, 10]    # 5连：10x

# 基础级联乘数 (普通模式)
BaseCoef:
  - 1   # 第一次 cascading：乘数为1
  - 2   # 第二次 cascading：乘数从 x1 提升至 x2
  - 3   # 第三次 cascading：乘数从 x2 提升至 x3
  - 5   # 第四次 cascading：乘数从 x3 提升至 x5

# 免费旋转级联乘数
FreeCoef:
  - 2   # 第一次 cascading：乘数从 x1 提升至 x2
  - 4   # 第二次 cascading：乘数从 x2 提升至 x4
  - 6   # 第三次 cascading：乘数从 x4 提升至 x6
  - 10  # 第四次 cascading：乘数从 x6 提升至 x10

# 图标权重配置
IconWeight:
  1: 100  # 二条
  2: 100  # 二筒
  3: 100  # 五条
  4: 100  # 五筒
  5: 100  # 八万
  6: 100  # 白板
  7: 100  # 中
  8: 100  # 发
  9: 0    # Wild (不直接出现)
  10: 30  # 胡 (Scatter)

# 免费旋转配置
FreeSpin:
  Icon: 10        # 胡作为 Scatter 触发免费旋转
  Number: 3       # 3个 Scatter 触发免费旋转
  FirstCount: 12  # 3个Scatter触发旋转次数为 12 次
  MoreCount: 2    # 每增加1个Scatter增加2次免费旋转
`

// Cascade游戏模块模板
const cascadeModuleTemplate = `package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m{{.GameID}}])

type m{{.GameID}} struct {
	Config c{{.GameID}}
}

type c{{.GameID}} struct {
	Row         int32           // 行数
	Column      int32           // 列数
	WildIcon    int32           // Wild符号ID
	PayoutTable [][3]int32      // 赔率表 [符号ID, 连线数, 赔率*100]
	FreeSpin    FreeSpinConfig{{.GameID}} // 免费旋转配置
	BaseCoef    []int32         // 普通模式级联乘数
	FreeCoef    []int32         // 免费旋转模式级联乘数
	MaxPayout   int32           // 最大派彩
	IconWeight  map[int32]int32 // 符号权重
}

type FreeSpinConfig{{.GameID}} struct {
	Icon       int32 // 触发免费旋转的符号ID
	Number     int   // 触发免费旋转的最小符号数
	FirstCount int   // 首次免费旋转次数
	MoreCount  int   // 额外触发免费旋转的次数
}

func (m *m{{.GameID}}) ID() int32 {
	return {{.GameID}}
}

func (m *m{{.GameID}}) Line() int32 {
	return {{.Lines}}
}

func (m m{{.GameID}}) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m{{.GameID}}) Exception(code int32) string {
	return (&games.S{{.GameID}}{}).Exception(code)
}

func (m *m{{.GameID}}) Init(config []byte) {
	m.Config = utils.ParseYAML[c{{.GameID}}](config)
}

func (m *m{{.GameID}}) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m{{.GameID}}) Spin(rd *rand.Rand) basic.ISpin {
	// TODO: 实现级联消除逻辑
	return &games.S{{.GameID}}{
		Pays: 0,
	}
}

func (m *m{{.GameID}}) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	return spin
}

func (m m{{.GameID}}) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m{{.GameID}}) InputCoef(ctl int32) int32 {
	return 100
}

func (m m{{.GameID}}) MinPayout(ctl int32) int32 {
	return 0
}
`

// Cascade游戏HTTP模板
const cascadeHTTPTemplate = `package games

import (
	"encoding/json"
	"igameCommon/basic"
)

type S{{.GameID}} struct {
	Pays     int32         // 总赔率
	Cascades []C{{.GameID}} // 级联数据
}

type C{{.GameID}} struct {
	Grid       []int32 // 网格数据
	Pay        int32   // 本次级联赔率
	Multiplier int32   // 乘数
	Removes    []int32 // 消除位置
}

func (s *S{{.GameID}}) Payout() int32 {
	return s.Pays
}

func (s *S{{.GameID}}) RoundPayouts() []int32 {
	return []int32{s.Payout()}
}

func (s *S{{.GameID}}) Data(ctx basic.SpinContext) string {
	b, _ := json.Marshal(s.parseDetail())
	return string(b)
}

func (s *S{{.GameID}}) RoundData(index int32, ctx basic.SpinContext) string {
	return s.Data(ctx)
}

func (s *S{{.GameID}}) Tags() []string {
	return []string{basic.EnumSpinTag.NORM}
}

func (s *S{{.GameID}}) Exception(code int32) string {
	return "{}"
}

func (s *S{{.GameID}}) parseDetail() map[string]interface{} {
	return map[string]interface{}{
		"pays":     s.Pays,
		"cascades": s.Cascades,
	}
}
`

// Cascade游戏配置模板
const cascadeConfigTemplate = `# 游戏 {{.GameID}} 配置文件
# 游戏类型: {{.GameType}} (级联消除)

Row: {{.Row}}
Column: {{.Column}}
MaxPayout: {{.MaxPayout}}

# Wild图标配置
WildIcon: 9

# 赔率表配置 (格式: [符号ID, 连线数, 赔率*100])
PayoutTable:
  # 高价值图标
  - [8, 3, 20]    # 3连：20x
  - [8, 4, 80]    # 4连：80x
  - [8, 5, 200]   # 5连：200x
  - [7, 3, 15]    # 3连：15x
  - [7, 4, 60]    # 4连：60x
  - [7, 5, 150]   # 5连：150x
  # 中价值图标
  - [6, 3, 10]    # 3连：10x
  - [6, 4, 40]    # 4连：40x
  - [6, 5, 100]   # 5连：100x
  - [5, 3, 8]     # 3连：8x
  - [5, 4, 30]    # 4连：30x
  - [5, 5, 80]    # 5连：80x
  # 低价值图标
  - [4, 3, 5]     # 3连：5x
  - [4, 4, 20]    # 4连：20x
  - [4, 5, 50]    # 5连：50x
  - [3, 3, 5]     # 3连：5x
  - [3, 4, 20]    # 4连：20x
  - [3, 5, 50]    # 5连：50x
  - [2, 3, 3]     # 3连：3x
  - [2, 4, 15]    # 4连：15x
  - [2, 5, 30]    # 5连：30x
  - [1, 3, 3]     # 3连：3x
  - [1, 4, 15]    # 4连：15x
  - [1, 5, 30]    # 5连：30x

# 级联乘数配置 (普通模式)
BaseCoef:
  - 1   # 第一次级联：乘数为1
  - 2   # 第二次级联：乘数为2
  - 4   # 第三次级联：乘数为4
  - 8   # 第四次级联：乘数为8
  - 16  # 第五次级联：乘数为16

# 免费旋转级联乘数
FreeCoef:
  - 2   # 第一次级联：乘数为2
  - 4   # 第二次级联：乘数为4
  - 8   # 第三次级联：乘数为8
  - 16  # 第四次级联：乘数为16
  - 32  # 第五次级联：乘数为32

# 图标权重配置
IconWeight:
  1: 120  # 低价值图标权重较高
  2: 120
  3: 100
  4: 100
  5: 80
  6: 80
  7: 60
  8: 40
  9: 5    # Wild图标权重较低
  10: 20  # Scatter图标

# 免费旋转配置
FreeSpin:
  Icon: 10        # Scatter图标触发免费旋转
  Number: 3       # 3个 Scatter 触发免费旋转
  FirstCount: 15  # 首次免费旋转15次
  MoreCount: 5    # 每增加1个Scatter增加5次免费旋转
`
