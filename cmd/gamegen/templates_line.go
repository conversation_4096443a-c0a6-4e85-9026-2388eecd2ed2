package main

// Line游戏模块模板
const lineModuleTemplate = `package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m{{.GameID}}])

type m{{.GameID}} struct {
	Config c{{.GameID}}
}

type c{{.GameID}} struct {
	GameID      int                   // 游戏ID
	RuleID      int                   // 生成器规则ID
	Row         int32                 // 网格行数
	Column      int32                 // 网格列数
	MaxPayout   int32                 // 最大支付
	WildIcon    int16                 // Wild图标
	ScatterIcon int16                 // Scatter图标
	PayoutTable map[int16][]int16     // 支付表
	IconWeight  map[int16]int         // 图标权重
	Pattern     [][]basic.Position    // 连线模式
	FreeSpin    FreeSpinConfig{{.GameID}} // 免费旋转配置
}

type FreeSpinConfig{{.GameID}} struct {
	Icon       int16 // 触发免费旋转的符号ID
	MinCount   int   // 触发免费旋转的最小符号数
	FreeCount  int   // 免费旋转次数
}

func (m m{{.GameID}}) ID() int32 {
	return {{.GameID}}
}

func (m m{{.GameID}}) Line() int32 {
	return {{.Lines}}
}

func (m m{{.GameID}}) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m{{.GameID}}) Exception(code int32) string {
	return games.S{{.GameID}}{}.Exception(code)
}

func (m *m{{.GameID}}) Init(config []byte) {
	m.Config = utils.ParseYAML[c{{.GameID}}](config)
}

func (m *m{{.GameID}}) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m{{.GameID}}) Spin(rd *rand.Rand) basic.ISpin {
	// TODO: 实现连线游戏逻辑
	return games.S{{.GameID}}{
		Pays: 0,
	}
}

func (m *m{{.GameID}}) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	return spin
}

func (m m{{.GameID}}) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m{{.GameID}}) InputCoef(ctl int32) int32 {
	return 100
}

func (m m{{.GameID}}) MinPayout(ctl int32) int32 {
	return 0
}
`

// Line游戏HTTP模板
const lineHTTPTemplate = `package games

import (
	"igameCommon/basic"
)

type S{{.GameID}} struct {
	Pays      int32
	Pages     []P{{.GameID}}
	FreeSpins int16
	Detail    string
}

type P{{.GameID}} struct {
	Grid    basic.Grid
	Pay     int32
	Free    int16
	Lines   []L{{.GameID}}
}

type L{{.GameID}} struct {
	ID        int     // 连线ID
	Positions []int   // 连线位置
	Icon      int16   // 图标
	Count     int     // 连线数量
	Value     int32   // 连线价值
}

func (s S{{.GameID}}) Payout() int32 {
	return s.Pays
}

func (s S{{.GameID}}) RoundPayouts() []int32 {
	return []int32{s.Pays}
}

func (s S{{.GameID}}) Data(ctx basic.SpinContext) string {
	return s.Detail
}

func (s S{{.GameID}}) RoundData(index int32, ctx basic.SpinContext) string {
	return s.Data(ctx)
}

func (s S{{.GameID}}) Tag() string {
	if len(s.Pages) > 0 && s.Pages[0].Free > 0 {
		return basic.EnumSpinTag.FREE
	}
	return basic.EnumSpinTag.NORM
}

func (s S{{.GameID}}) Tags() []string {
	return []string{s.Tag()}
}

func (s S{{.GameID}}) Exception(code int32) string {
	return "{}"
}
`

// Line游戏配置模板
const lineConfigTemplate = `# 游戏 {{.GameID}} 配置文件
# 游戏类型: {{.GameType}} (连线玩法)

GameID: {{.GameID}}
RuleID: 1
Row: {{.Row}}
Column: {{.Column}}
MaxPayout: {{.MaxPayout}}

# 特殊图标配置
WildIcon: 9      # Wild图标ID
ScatterIcon: 10  # Scatter图标ID

# 支付表配置 (图标ID -> [3连, 4连, 5连] 赔率)
PayoutTable:
  1: [3, 8, 25]    # 低价值图标
  2: [3, 8, 25]
  3: [5, 12, 40]
  4: [5, 12, 40]
  5: [8, 20, 60]
  6: [10, 25, 80]
  7: [15, 50, 120]
  8: [20, 80, 200]

# 图标权重配置
IconWeight:
  1: 120  # 低价值图标权重较高
  2: 120
  3: 100
  4: 100
  5: 80
  6: 60
  7: 40
  8: 30
  9: 15   # Wild图标权重较低
  10: 8   # Scatter图标权重最低

# 连线模式配置 (定义{{.Lines}}条连线)
Pattern:
  # 第1条线: 中间行
  - [{Row: 1, Column: 0}, {Row: 1, Column: 1}, {Row: 1, Column: 2}, {Row: 1, Column: 3}, {Row: 1, Column: 4}]
  # 第2条线: 上行
  - [{Row: 0, Column: 0}, {Row: 0, Column: 1}, {Row: 0, Column: 2}, {Row: 0, Column: 3}, {Row: 0, Column: 4}]
  # 第3条线: 下行
  - [{Row: 2, Column: 0}, {Row: 2, Column: 1}, {Row: 2, Column: 2}, {Row: 2, Column: 3}, {Row: 2, Column: 4}]
  # 第4条线: V形
  - [{Row: 0, Column: 0}, {Row: 1, Column: 1}, {Row: 2, Column: 2}, {Row: 1, Column: 3}, {Row: 0, Column: 4}]
  # 第5条线: 倒V形
  - [{Row: 2, Column: 0}, {Row: 1, Column: 1}, {Row: 0, Column: 2}, {Row: 1, Column: 3}, {Row: 2, Column: 4}]
  # 第6条线: W形
  - [{Row: 0, Column: 0}, {Row: 2, Column: 1}, {Row: 0, Column: 2}, {Row: 2, Column: 3}, {Row: 0, Column: 4}]
  # 第7条线: M形
  - [{Row: 2, Column: 0}, {Row: 0, Column: 1}, {Row: 2, Column: 2}, {Row: 0, Column: 3}, {Row: 2, Column: 4}]
  # 第8条线: 上升
  - [{Row: 2, Column: 0}, {Row: 1, Column: 1}, {Row: 1, Column: 2}, {Row: 1, Column: 3}, {Row: 0, Column: 4}]
  # 第9条线: 下降
  - [{Row: 0, Column: 0}, {Row: 1, Column: 1}, {Row: 1, Column: 2}, {Row: 1, Column: 3}, {Row: 2, Column: 4}]
  # 第10条线: 波浪1
  - [{Row: 1, Column: 0}, {Row: 0, Column: 1}, {Row: 1, Column: 2}, {Row: 2, Column: 3}, {Row: 1, Column: 4}]
  # 第11条线: 波浪2
  - [{Row: 1, Column: 0}, {Row: 2, Column: 1}, {Row: 1, Column: 2}, {Row: 0, Column: 3}, {Row: 1, Column: 4}]
  # 第12条线: 锯齿1
  - [{Row: 0, Column: 0}, {Row: 2, Column: 1}, {Row: 1, Column: 2}, {Row: 2, Column: 3}, {Row: 0, Column: 4}]
  # 第13条线: 锯齿2
  - [{Row: 2, Column: 0}, {Row: 0, Column: 1}, {Row: 1, Column: 2}, {Row: 0, Column: 3}, {Row: 2, Column: 4}]
  # 第14条线: 阶梯上
  - [{Row: 2, Column: 0}, {Row: 2, Column: 1}, {Row: 1, Column: 2}, {Row: 0, Column: 3}, {Row: 0, Column: 4}]
  # 第15条线: 阶梯下
  - [{Row: 0, Column: 0}, {Row: 0, Column: 1}, {Row: 1, Column: 2}, {Row: 2, Column: 3}, {Row: 2, Column: 4}]
  # 第16条线: 双峰
  - [{Row: 1, Column: 0}, {Row: 0, Column: 1}, {Row: 2, Column: 2}, {Row: 0, Column: 3}, {Row: 1, Column: 4}]
  # 第17条线: 双谷
  - [{Row: 1, Column: 0}, {Row: 2, Column: 1}, {Row: 0, Column: 2}, {Row: 2, Column: 3}, {Row: 1, Column: 4}]
  # 第18条线: 蛇形1
  - [{Row: 0, Column: 0}, {Row: 1, Column: 1}, {Row: 2, Column: 2}, {Row: 2, Column: 3}, {Row: 1, Column: 4}]
  # 第19条线: 蛇形2
  - [{Row: 2, Column: 0}, {Row: 1, Column: 1}, {Row: 0, Column: 2}, {Row: 0, Column: 3}, {Row: 1, Column: 4}]
  # 第20条线: 复合波
  - [{Row: 0, Column: 0}, {Row: 2, Column: 1}, {Row: 2, Column: 2}, {Row: 1, Column: 3}, {Row: 0, Column: 4}]

# 免费旋转配置
FreeSpin:
  Icon: 10        # Scatter图标触发免费旋转
  MinCount: 3     # 最少3个Scatter触发
  FreeCount: 12   # 免费旋转12次
`
