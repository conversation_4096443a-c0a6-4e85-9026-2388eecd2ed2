package main

import (
	"bytes"
	"fmt"
	"text/template"
)

// TemplateManager 模板管理器
type TemplateManager struct {
	moduleTemplates map[GameType]*template.Template
	httpTemplates   map[GameType]*template.Template
	configTemplates map[GameType]*template.Template
}

// NewTemplateManager 创建模板管理器
func NewTemplateManager() (*TemplateManager, error) {
	tm := &TemplateManager{
		moduleTemplates: make(map[GameType]*template.Template),
		httpTemplates:   make(map[GameType]*template.Template),
		configTemplates: make(map[GameType]*template.Template),
	}

	if err := tm.loadTemplates(); err != nil {
		return nil, err
	}

	return tm, nil
}

// loadTemplates 加载所有模板
func (tm *TemplateManager) loadTemplates() error {
	gameTypes := []GameType{GameTypeSlot, GameTypeWays, GameTypeCascade, GameTypeLine}

	for _, gameType := range gameTypes {
		// 加载模块模板
		moduleTemplate, err := template.New("module").Parse(tm.getModuleTemplate(gameType))
		if err != nil {
			return fmt.Errorf("解析模块模板失败 (%s): %v", gameType, err)
		}
		tm.moduleTemplates[gameType] = moduleTemplate

		// 加载HTTP模板
		httpTemplate, err := template.New("http").Parse(tm.getHTTPTemplate(gameType))
		if err != nil {
			return fmt.Errorf("解析HTTP模板失败 (%s): %v", gameType, err)
		}
		tm.httpTemplates[gameType] = httpTemplate

		// 加载配置模板
		configTemplate, err := template.New("config").Parse(tm.getConfigTemplate(gameType))
		if err != nil {
			return fmt.Errorf("解析配置模板失败 (%s): %v", gameType, err)
		}
		tm.configTemplates[gameType] = configTemplate
	}

	return nil
}

// RenderModule 渲染模块文件
func (tm *TemplateManager) RenderModule(config GameConfig) (string, error) {
	template := tm.moduleTemplates[config.GameType]
	if template == nil {
		return "", fmt.Errorf("未找到游戏类型 %s 的模块模板", config.GameType)
	}

	var buf bytes.Buffer
	if err := template.Execute(&buf, config); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// RenderHTTPGame 渲染HTTP响应文件
func (tm *TemplateManager) RenderHTTPGame(config GameConfig) (string, error) {
	template := tm.httpTemplates[config.GameType]
	if template == nil {
		return "", fmt.Errorf("未找到游戏类型 %s 的HTTP模板", config.GameType)
	}

	var buf bytes.Buffer
	if err := template.Execute(&buf, config); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// RenderConfig 渲染配置文件
func (tm *TemplateManager) RenderConfig(config GameConfig) (string, error) {
	template := tm.configTemplates[config.GameType]
	if template == nil {
		return "", fmt.Errorf("未找到游戏类型 %s 的配置模板", config.GameType)
	}

	var buf bytes.Buffer
	if err := template.Execute(&buf, config); err != nil {
		return "", err
	}

	return buf.String(), nil
}

// getModuleTemplate 获取模块模板
func (tm *TemplateManager) getModuleTemplate(gameType GameType) string {
	switch gameType {
	case GameTypeSlot:
		return slotModuleTemplate
	case GameTypeWays:
		return waysModuleTemplate
	case GameTypeCascade:
		return cascadeModuleTemplate
	case GameTypeLine:
		return lineModuleTemplate
	default:
		return slotModuleTemplate // 默认使用slot模板
	}
}

// getHTTPTemplate 获取HTTP模板
func (tm *TemplateManager) getHTTPTemplate(gameType GameType) string {
	switch gameType {
	case GameTypeSlot:
		return slotHTTPTemplate
	case GameTypeWays:
		return waysHTTPTemplate
	case GameTypeCascade:
		return cascadeHTTPTemplate
	case GameTypeLine:
		return lineHTTPTemplate
	default:
		return slotHTTPTemplate // 默认使用slot模板
	}
}

// getConfigTemplate 获取配置模板
func (tm *TemplateManager) getConfigTemplate(gameType GameType) string {
	switch gameType {
	case GameTypeSlot:
		return slotConfigTemplate
	case GameTypeWays:
		return waysConfigTemplate
	case GameTypeCascade:
		return cascadeConfigTemplate
	case GameTypeLine:
		return lineConfigTemplate
	default:
		return slotConfigTemplate // 默认使用slot模板
	}
}

// Slot游戏模块模板
const slotModuleTemplate = `package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m{{.GameID}}])

type m{{.GameID}} struct {
	Config c{{.GameID}}
}

type c{{.GameID}} struct {
	GameID      int                   // 游戏ID
	RuleID      int                   // 生成器规则ID
	Row         int32                 // 网格行数
	Column      int32                 // 网格列数
	MaxPayout   int32                 // 最大支付
	WildIcon    int16                 // Wild图标
	ScatterIcon int16                 // Scatter图标
	PayoutTable map[int16][]int16     // 支付表
	IconWeight  map[int16]int         // 图标权重
	FreeSpin    FreeSpinConfig{{.GameID}} // 免费旋转配置
}

type FreeSpinConfig{{.GameID}} struct {
	Icon       int16 // 触发免费旋转的符号ID
	MinCount   int   // 触发免费旋转的最小符号数
	FreeCount  int   // 免费旋转次数
}

func (m m{{.GameID}}) ID() int32 {
	return {{.GameID}}
}

func (m m{{.GameID}}) Line() int32 {
	return {{.Lines}}
}

func (m m{{.GameID}}) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m{{.GameID}}) Exception(code int32) string {
	return games.S{{.GameID}}{}.Exception(code)
}

func (m *m{{.GameID}}) Init(config []byte) {
	m.Config = utils.ParseYAML[c{{.GameID}}](config)
}

func (m *m{{.GameID}}) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m{{.GameID}}) Spin(rd *rand.Rand) basic.ISpin {
	// TODO: 实现游戏逻辑
	return games.S{{.GameID}}{
		Pays: 0,
	}
}

func (m *m{{.GameID}}) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	return spin
}

func (m m{{.GameID}}) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m{{.GameID}}) InputCoef(ctl int32) int32 {
	return 100
}

func (m m{{.GameID}}) MinPayout(ctl int32) int32 {
	return 0
}
`

// Slot游戏HTTP模板
const slotHTTPTemplate = `package games

import (
	"igameCommon/basic"
)

type S{{.GameID}} struct {
	Pays      int32
	Pages     []P{{.GameID}}
	FreeSpins int16
	Detail    string
}

type P{{.GameID}} struct {
	Grid    basic.Grid
	Pay     int32
	Free    int16
	Pattern uint64
}

func (s S{{.GameID}}) Payout() int32 {
	return s.Pays
}

func (s S{{.GameID}}) RoundPayouts() []int32 {
	return []int32{s.Pays}
}

func (s S{{.GameID}}) Data(ctx basic.SpinContext) string {
	return s.Detail
}

func (s S{{.GameID}}) RoundData(index int32, ctx basic.SpinContext) string {
	return s.Data(ctx)
}

func (s S{{.GameID}}) Tag() string {
	if len(s.Pages) > 0 && s.Pages[0].Free > 0 {
		return basic.EnumSpinTag.FREE
	}
	return basic.EnumSpinTag.NORM
}

func (s S{{.GameID}}) Tags() []string {
	return []string{s.Tag()}
}

func (s S{{.GameID}}) Exception(code int32) string {
	return "{}"
}
`

// Slot游戏配置模板
const slotConfigTemplate = `# 游戏 {{.GameID}} 配置文件
# 游戏类型: {{.GameType}}

GameID: {{.GameID}}
RuleID: 1
Row: {{.Row}}
Column: {{.Column}}
MaxPayout: {{.MaxPayout}}

# 特殊图标配置
WildIcon: 9      # Wild图标ID
ScatterIcon: 10  # Scatter图标ID

# 支付表配置 (图标ID -> [3连, 4连, 5连] 赔率)
PayoutTable:
  1: [2, 5, 10]    # 低价值图标
  2: [2, 5, 10]
  3: [4, 10, 20]
  4: [4, 10, 20]
  5: [6, 15, 40]
  6: [8, 20, 60]
  7: [10, 40, 80]
  8: [15, 60, 100]

# 图标权重配置
IconWeight:
  1: 100  # 低价值图标权重较高
  2: 100
  3: 80
  4: 80
  5: 60
  6: 40
  7: 30
  8: 20
  9: 10   # Wild图标权重较低
  10: 5   # Scatter图标权重最低

# 免费旋转配置
FreeSpin:
  Icon: 10        # Scatter图标触发免费旋转
  MinCount: 3     # 最少3个Scatter触发
  FreeCount: 10   # 免费旋转10次
`

// Ways游戏模块模板
const waysModuleTemplate = `package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m{{.GameID}}])

type m{{.GameID}} struct {
	Config c{{.GameID}}
}

type c{{.GameID}} struct {
	Row         int32           // 行数
	Column      int32           // 列数
	WildIcon    int32           // Wild符号ID
	PayoutTable [][3]int32      // 赔率表 [符号ID, 连线数, 赔率*100]
	FreeSpin    FreeSpinConfig{{.GameID}} // 免费旋转配置
	BaseCoef    []int32         // 普通模式级联乘数
	FreeCoef    []int32         // 免费旋转模式级联乘数
	MaxPayout   int32           // 最大派彩
	IconWeight  map[int32]int32 // 符号权重
}

type FreeSpinConfig{{.GameID}} struct {
	Icon       int32 // 触发免费旋转的符号ID
	Number     int   // 触发免费旋转的最小符号数
	FirstCount int   // 首次免费旋转次数
	MoreCount  int   // 额外触发免费旋转的次数
}

func (m *m{{.GameID}}) ID() int32 {
	return {{.GameID}}
}

func (m *m{{.GameID}}) Line() int32 {
	return {{.Lines}}
}

func (m m{{.GameID}}) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m{{.GameID}}) Exception(code int32) string {
	return (&games.S{{.GameID}}{}).Exception(code)
}

func (m *m{{.GameID}}) Init(config []byte) {
	m.Config = utils.ParseYAML[c{{.GameID}}](config)
}

func (m *m{{.GameID}}) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m{{.GameID}}) Spin(rd *rand.Rand) basic.ISpin {
	// TODO: 实现Ways玩法逻辑
	return &games.S{{.GameID}}{
		Pays: 0,
	}
}

func (m *m{{.GameID}}) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	return spin
}

func (m m{{.GameID}}) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m{{.GameID}}) InputCoef(ctl int32) int32 {
	return 100
}

func (m m{{.GameID}}) MinPayout(ctl int32) int32 {
	return 0
}
`
