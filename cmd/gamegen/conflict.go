package main

import (
	"bufio"
	"fmt"
	"os"
	"path/filepath"
	"strings"
	"time"
)

// ConflictResolver 文件冲突解决器
type ConflictResolver struct {
	workDir string
}

// NewConflictResolver 创建文件冲突解决器
func NewConflictResolver(workDir string) *ConflictResolver {
	return &ConflictResolver{
		workDir: workDir,
	}
}

// FileInfo 文件信息
type FileInfo struct {
	Path     string
	Exists   bool
	Size     int64
	ModTime  time.Time
	Writable bool
}

// CheckConflicts 检查文件冲突
func (cr *ConflictResolver) CheckConflicts(gameID int32) ([]FileInfo, error) {
	files := []string{
		fmt.Sprintf("modules/m%d.go", gameID),
		fmt.Sprintf("http/games/s%d.go", gameID),
		fmt.Sprintf("configs/%d.yaml", gameID),
	}

	var conflicts []FileInfo
	for _, file := range files {
		fullPath := filepath.Join(cr.workDir, file)
		info, err := cr.getFileInfo(fullPath, file)
		if err != nil {
			return nil, err
		}
		
		if info.Exists {
			conflicts = append(conflicts, info)
		}
	}

	return conflicts, nil
}

// getFileInfo 获取文件信息
func (cr *ConflictResolver) getFileInfo(fullPath, relativePath string) (FileInfo, error) {
	info := FileInfo{
		Path:   relativePath,
		Exists: false,
	}

	stat, err := os.Stat(fullPath)
	if os.IsNotExist(err) {
		return info, nil
	}
	if err != nil {
		return info, err
	}

	info.Exists = true
	info.Size = stat.Size()
	info.ModTime = stat.ModTime()

	// 检查文件是否可写
	file, err := os.OpenFile(fullPath, os.O_WRONLY, 0)
	if err == nil {
		info.Writable = true
		file.Close()
	}

	return info, nil
}

// ResolveConflicts 解决文件冲突
func (cr *ConflictResolver) ResolveConflicts(conflicts []FileInfo, force bool, interactive bool) error {
	if len(conflicts) == 0 {
		return nil
	}

	fmt.Printf("⚠️  发现 %d 个文件冲突:\n", len(conflicts))
	for _, conflict := range conflicts {
		cr.displayFileInfo(conflict)
	}

	if force {
		fmt.Printf("🔧 强制覆盖模式，将覆盖所有冲突文件\n")
		return cr.backupConflicts(conflicts)
	}

	if !interactive {
		return fmt.Errorf("文件冲突，使用 -force 参数强制覆盖或 -interactive 进入交互模式")
	}

	return cr.interactiveResolve(conflicts)
}

// displayFileInfo 显示文件信息
func (cr *ConflictResolver) displayFileInfo(info FileInfo) {
	fmt.Printf("  📄 %s\n", info.Path)
	fmt.Printf("     大小: %d 字节\n", info.Size)
	fmt.Printf("     修改时间: %s\n", info.ModTime.Format("2006-01-02 15:04:05"))
	if !info.Writable {
		fmt.Printf("     ⚠️  文件只读\n")
	}
	fmt.Println()
}

// interactiveResolve 交互式解决冲突
func (cr *ConflictResolver) interactiveResolve(conflicts []FileInfo) error {
	reader := bufio.NewReader(os.Stdin)

	fmt.Println("🤔 如何处理这些冲突文件?")
	fmt.Println("  1. 覆盖所有文件")
	fmt.Println("  2. 备份后覆盖")
	fmt.Println("  3. 逐个选择")
	fmt.Println("  4. 取消操作")

	for {
		fmt.Print("请选择 (1-4): ")
		input, _ := reader.ReadString('\n')
		input = strings.TrimSpace(input)

		switch input {
		case "1":
			fmt.Printf("✅ 将覆盖所有冲突文件\n")
			return nil
		case "2":
			fmt.Printf("✅ 将备份后覆盖所有冲突文件\n")
			return cr.backupConflicts(conflicts)
		case "3":
			return cr.selectiveResolve(conflicts)
		case "4":
			return fmt.Errorf("用户取消操作")
		default:
			fmt.Println("❌ 无效选择，请输入 1-4")
		}
	}
}

// selectiveResolve 选择性解决冲突
func (cr *ConflictResolver) selectiveResolve(conflicts []FileInfo) error {
	reader := bufio.NewReader(os.Stdin)
	var toBackup []FileInfo

	for _, conflict := range conflicts {
		fmt.Printf("\n📄 文件: %s\n", conflict.Path)
		cr.displayFileInfo(conflict)

		fmt.Println("处理方式:")
		fmt.Println("  1. 覆盖")
		fmt.Println("  2. 备份后覆盖")
		fmt.Println("  3. 跳过 (保留原文件)")

		for {
			fmt.Print("请选择 (1-3): ")
			input, _ := reader.ReadString('\n')
			input = strings.TrimSpace(input)

			switch input {
			case "1":
				fmt.Printf("✅ 将覆盖 %s\n", conflict.Path)
				goto nextFile
			case "2":
				fmt.Printf("✅ 将备份后覆盖 %s\n", conflict.Path)
				toBackup = append(toBackup, conflict)
				goto nextFile
			case "3":
				fmt.Printf("⏭️  跳过 %s\n", conflict.Path)
				goto nextFile
			default:
				fmt.Println("❌ 无效选择，请输入 1-3")
			}
		}
	nextFile:
	}

	if len(toBackup) > 0 {
		return cr.backupConflicts(toBackup)
	}

	return nil
}

// backupConflicts 备份冲突文件
func (cr *ConflictResolver) backupConflicts(conflicts []FileInfo) error {
	timestamp := time.Now().Format("20060102_150405")
	backupDir := filepath.Join(cr.workDir, "backup", timestamp)

	if err := os.MkdirAll(backupDir, 0755); err != nil {
		return fmt.Errorf("创建备份目录失败: %v", err)
	}

	fmt.Printf("📦 创建备份目录: %s\n", backupDir)

	for _, conflict := range conflicts {
		srcPath := filepath.Join(cr.workDir, conflict.Path)
		dstPath := filepath.Join(backupDir, conflict.Path)

		// 确保目标目录存在
		if err := os.MkdirAll(filepath.Dir(dstPath), 0755); err != nil {
			return fmt.Errorf("创建备份子目录失败: %v", err)
		}

		// 复制文件
		if err := cr.copyFile(srcPath, dstPath); err != nil {
			return fmt.Errorf("备份文件失败 (%s): %v", conflict.Path, err)
		}

		fmt.Printf("✅ 已备份: %s -> %s\n", conflict.Path, dstPath)
	}

	return nil
}

// copyFile 复制文件
func (cr *ConflictResolver) copyFile(src, dst string) error {
	srcFile, err := os.Open(src)
	if err != nil {
		return err
	}
	defer srcFile.Close()

	dstFile, err := os.Create(dst)
	if err != nil {
		return err
	}
	defer dstFile.Close()

	_, err = dstFile.ReadFrom(srcFile)
	return err
}

// ValidatePermissions 验证文件权限
func (cr *ConflictResolver) ValidatePermissions(gameID int32) error {
	files := []string{
		fmt.Sprintf("modules/m%d.go", gameID),
		fmt.Sprintf("http/games/s%d.go", gameID),
		fmt.Sprintf("configs/%d.yaml", gameID),
	}

	for _, file := range files {
		dir := filepath.Dir(filepath.Join(cr.workDir, file))
		
		// 检查目录是否存在，不存在则尝试创建
		if _, err := os.Stat(dir); os.IsNotExist(err) {
			if err := os.MkdirAll(dir, 0755); err != nil {
				return fmt.Errorf("无法创建目录 %s: %v", dir, err)
			}
		}

		// 检查目录写权限
		testFile := filepath.Join(dir, ".write_test")
		if err := os.WriteFile(testFile, []byte("test"), 0644); err != nil {
			return fmt.Errorf("目录 %s 没有写权限: %v", dir, err)
		}
		os.Remove(testFile) // 清理测试文件
	}

	return nil
}

// GenerateReport 生成冲突报告
func (cr *ConflictResolver) GenerateReport(conflicts []FileInfo) string {
	if len(conflicts) == 0 {
		return "✅ 没有发现文件冲突"
	}

	report := fmt.Sprintf("⚠️  发现 %d 个文件冲突:\n\n", len(conflicts))
	
	for i, conflict := range conflicts {
		report += fmt.Sprintf("%d. %s\n", i+1, conflict.Path)
		report += fmt.Sprintf("   大小: %d 字节\n", conflict.Size)
		report += fmt.Sprintf("   修改时间: %s\n", conflict.ModTime.Format("2006-01-02 15:04:05"))
		if !conflict.Writable {
			report += "   状态: 只读\n"
		}
		report += "\n"
	}

	return report
}
