# 游戏引擎框架生成器

一个强大的Go语言游戏引擎框架自动生成工具，能够基于游戏ID和配置参数自动生成完整的、可编译的游戏引擎框架。

## 功能特性

### 🎮 核心功能
- **自动代码生成**: 基于游戏ID生成完整的游戏框架代码
- **多游戏类型支持**: 支持Slot、Ways、Cascade、Line四种游戏类型
- **智能模板系统**: 针对不同游戏类型提供专门优化的代码模板
- **配置文件生成**: 自动生成YAML格式的游戏配置文件

### 🛠️ 高级特性
- **交互式配置**: 提供友好的交互式配置界面
- **文件冲突处理**: 智能检测和处理文件冲突，支持备份
- **代码验证**: 自动验证生成代码的语法和编译性
- **权限检查**: 验证文件系统权限，确保生成过程顺利

### 📁 生成文件结构
```
modules/m{gameID}.go      # 游戏逻辑模块
http/games/s{gameID}.go   # HTTP响应处理器
configs/{gameID}.yaml     # 游戏配置文件
```

## 安装和使用

### 编译
```bash
cd cmd/gamegen
go build -o gamegen .
```

### 基本用法

#### 1. 快速生成
```bash
# 生成一个基本的slot游戏
./gamegen -id=10000999 -type=slot

# 生成ways玩法游戏
./gamegen -id=10001000 -type=ways -row=4 -column=6

# 生成级联消除游戏
./gamegen -id=10001001 -type=cascade -row=6 -column=6 -max-payout=1000000
```

#### 2. 交互式配置
```bash
# 启动交互式配置模式
./gamegen -id=10000999 -interactive
```

#### 3. 强制覆盖模式
```bash
# 强制覆盖现有文件
./gamegen -id=10000999 -type=slot -force
```

### 命令行参数

| 参数 | 类型 | 默认值 | 说明 |
|------|------|--------|------|
| `-id` | int | 0 | 游戏ID (必需) |
| `-type` | string | "slot" | 游戏类型 (slot/ways/cascade/line) |
| `-row` | int | 4 | 网格行数 |
| `-column` | int | 5 | 网格列数 |
| `-lines` | int | 20 | 线数 |
| `-max-payout` | int | 250000 | 最大奖金 |
| `-interactive` | bool | false | 交互式配置模式 |
| `-force` | bool | false | 强制覆盖现有文件 |
| `-validate` | bool | true | 验证生成的代码 |

## 游戏类型详解

### 1. Slot (传统老虎机)
- **特点**: 经典老虎机玩法，支持多条连线
- **建议配置**: 3x5 或 4x5 网格，20-25条线
- **适用场景**: 传统老虎机游戏

### 2. Ways (Ways玩法)
- **特点**: 243/1024 Ways玩法，相邻符号连线
- **建议配置**: 4x5 (243 Ways) 或 4x6 (1024 Ways)
- **适用场景**: 现代老虎机，更多获胜方式

### 3. Cascade (级联消除)
- **特点**: 消除类玩法，支持级联乘数
- **建议配置**: 6x6 或 5x5 网格
- **适用场景**: 消除类游戏，如糖果传奇风格

### 4. Line (连线玩法)
- **特点**: 自定义连线模式的老虎机
- **建议配置**: 3x5 网格，20条预定义连线
- **适用场景**: 需要特定连线模式的游戏

## 交互式配置指南

启动交互式模式后，系统将引导您完成以下配置：

### 1. 游戏类型选择
```
📋 选择游戏类型:
  1. slot    - 传统老虎机
  2. ways    - Ways玩法
  3. cascade - 级联消除
  4. line    - 连线玩法
```

### 2. 网格配置
- 系统会根据选择的游戏类型提供建议
- 可以自定义行数和列数

### 3. 线数配置
- Slot/Line: 手动设置线数
- Ways: 自动计算 (行数 × 列数)
- Cascade: 固定为1

### 4. 奖金配置
- 根据游戏类型提供建议的最大奖金范围

### 5. 高级选项
- 文件覆盖策略
- 代码验证选项

## 生成的代码结构

### 模块文件 (modules/m{gameID}.go)
```go
// 实现 basic.IModule 接口
type m{gameID} struct {
    Config c{gameID}
}

// 核心方法
func (m *m{gameID}) ID() int32
func (m *m{gameID}) Line() int32
func (m *m{gameID}) Spin(rd *rand.Rand) basic.ISpin
func (m *m{gameID}) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin
// ... 其他接口方法
```

### HTTP响应文件 (http/games/s{gameID}.go)
```go
// 实现 basic.ISpin 接口
type S{gameID} struct {
    Pays int32
    // ... 游戏特定字段
}

// 核心方法
func (s S{gameID}) Payout() int32
func (s S{gameID}) Data(ctx basic.SpinContext) string
// ... 其他接口方法
```

### 配置文件 (configs/{gameID}.yaml)
```yaml
# 游戏基本配置
GameID: {gameID}
Row: {row}
Column: {column}
MaxPayout: {maxPayout}

# 图标配置
PayoutTable:
  # 支付表配置

IconWeight:
  # 图标权重配置

# 免费旋转配置
FreeSpin:
  # 免费旋转参数
```

## 文件冲突处理

当检测到文件冲突时，系统提供多种处理方式：

### 1. 自动处理
- **强制模式** (`-force`): 直接覆盖所有冲突文件
- **非交互模式**: 报错并退出

### 2. 交互式处理
```
🤔 如何处理这些冲突文件?
  1. 覆盖所有文件
  2. 备份后覆盖
  3. 逐个选择
  4. 取消操作
```

### 3. 备份机制
- 自动创建带时间戳的备份目录
- 保留原文件的完整目录结构
- 备份路径: `backup/{timestamp}/`

## 代码验证

生成器包含完整的代码验证功能：

### 1. 语法检查
- 使用 `go fmt` 检查Go语法
- 使用 `go vet` 进行代码质量检查

### 2. 编译验证
- 验证整个项目可以正常编译
- 检查模块依赖关系

### 3. 自动测试
- 生成基本的单元测试文件
- 验证生成的模块基本功能

## 集成工作流

生成框架后的推荐工作流：

### 1. 生成数据
```bash
go run cmd/generate/main.go {gameID}
```

### 2. 生成支付表
```bash
go run cmd/paytab/main.go {gameID}
```

### 3. 运行测试
```bash
make test id={gameID}
```

### 4. 启动服务
```bash
go run main.go
```

## 故障排除

### 常见问题

#### 1. 权限错误
```
错误: 目录 modules 没有写权限
解决: chmod 755 modules
```

#### 2. 编译错误
```
错误: 项目编译失败
解决: 检查依赖，运行 go mod tidy
```

#### 3. 文件冲突
```
错误: 文件冲突，使用 -force 参数强制覆盖
解决: 使用 -force 或 -interactive 参数
```

### 调试模式
```bash
# 启用详细输出
./gamegen -id=10000999 -interactive -validate=true
```

## 扩展开发

### 添加新游戏类型

1. 在 `GameType` 枚举中添加新类型
2. 创建对应的模板文件
3. 在 `TemplateManager` 中注册模板
4. 更新交互式配置逻辑

### 自定义模板

模板文件位于：
- `templates.go` - Slot游戏模板
- `templates_ways.go` - Ways和Cascade模板
- `templates_line.go` - Line游戏模板

## 许可证

本项目遵循项目根目录的许可证条款。

## 贡献

欢迎提交Issue和Pull Request来改进这个工具。

---

**注意**: 生成的代码仅为框架模板，实际游戏逻辑需要根据具体需求进行实现。
