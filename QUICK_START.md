# 游戏引擎框架生成器 - 快速开始指南

## 🚀 5分钟快速上手

### 1. 生成游戏框架

选择以下任一方式生成游戏框架：

#### 方式A：基础生成器（推荐新手）
```bash
python generate_game.py 10000999
```

#### 方式B：高级生成器（推荐有经验用户）
```bash
# 生成传统老虎机
python generate_game_advanced.py 10000999 --type slot --lines 25

# 生成Ways玩法
python generate_game_advanced.py 10000888 --type ways --rows 6 --columns 5

# 交互式配置
python generate_game_advanced.py --interactive
```

### 2. 验证生成结果

```bash
python integrate_game.py 10000999
```

### 3. 构建游戏

运行生成的集成脚本：
```bash
./integrate_10000999.sh
```

或手动执行：
```bash
# 生成游戏数据
cd bin && go run ../cmd/generate/main.go 10000999

# 生成支付表
cd bin && go run ../cmd/paytab/main.go 10000999 0

# 测试游戏
go test -bench=BenchmarkSpin -v ./test -args 10000999
```

## 📁 生成的文件结构

```
项目根目录/
├── modules/
│   └── m10000999.go          # 游戏逻辑模块
├── http/games/
│   └── s10000999.go          # HTTP接口处理
├── configs/
│   └── 10000999.yaml         # 游戏配置文件
└── integrate_10000999.sh     # 集成脚本
```

## ⚙️ 自定义配置

### 修改游戏参数

编辑 `configs/10000999.yaml`：

```yaml
# 基础配置
GameID: 10000999
Row: 4                    # 网格行数
Column: 5                 # 网格列数
MaxPayout: 250000         # 最大奖金

# 支付表 [图标ID, 连线数, 倍率]
PayoutTable:
  - [8, 3, 50]           # 图标8，3连，50倍
  - [8, 4, 200]          # 图标8，4连，200倍
  - [8, 5, 500]          # 图标8，5连，500倍

# 图标权重（出现概率）
IconWeight:
  1: 150                 # 图标1权重150
  2: 150                 # 图标2权重150
  # ... 更多图标配置
```

### 实现游戏逻辑

在 `modules/m10000999.go` 中完善：

```go
// 主要需要实现的方法
func (m *m10000999) Spin(rd *rand.Rand) basic.ISpin {
    // TODO: 实现旋转逻辑
}

func (m *m10000999) calculateLineWins(grid basic.Grid) ([]games.L10000999, int32) {
    // TODO: 实现中奖计算
}
```

## 🎮 游戏类型说明

### Slot（传统老虎机）
- 使用固定支付线
- 符号必须在支付线上连续
- 适合经典老虎机玩法

### Ways（Ways玩法）
- 符号从左到右连续即可中奖
- 不依赖固定支付线
- 更高的中奖频率

### Cascade（级联消除）
- 中奖符号消除后上方符号下落
- 支持连续级联
- 类似消消乐玩法

### Poker（扑克游戏）
- 基于扑克牌型
- 支持换牌功能
- 策略性玩法

## 🔧 常用命令

### 开发阶段
```bash
# 生成新游戏
python generate_game_advanced.py 10000999 --type ways

# 验证集成
python integrate_game.py 10000999

# 测试生成器
python test_generator.py
```

### 构建阶段
```bash
# 生成数据
cd bin && go run ../cmd/generate/main.go 10000999

# 生成支付表
cd bin && go run ../cmd/paytab/main.go 10000999 0

# 编译检查
go build modules/m10000999.go
go build http/games/s10000999.go
```

### 测试阶段
```bash
# 单元测试
go test -v ./test -args 10000999

# 性能测试
go test -bench=BenchmarkSpin -v ./test -args 10000999

# 模拟测试
cd bin && go run ../cmd/simulate/main.go 10000999 0 0.05 1000000
```

## 🐛 常见问题

### Q: 生成的代码编译失败
**A:** 检查以下几点：
1. 确保Go环境正确配置
2. 检查生成的代码语法
3. 确认依赖包已正确导入

### Q: 配置文件格式错误
**A:** 使用以下命令验证：
```bash
python integrate_game.py 10000999
```

### Q: 游戏ID已存在
**A:** 生成器会提示是否覆盖，或使用新的游戏ID

### Q: 权限错误
**A:** 确保对目标目录有写入权限：
```bash
chmod 755 /path/to/workspace
```

## 📚 进阶使用

### 自定义模板
1. 复制现有模板
2. 修改模板内容
3. 使用 `--template` 参数

### 批量生成
```bash
# 生成多个游戏
for id in 10000001 10000002 10000003; do
    python generate_game_advanced.py $id --type slot
done
```

### 配置管理
```bash
# 备份配置
cp configs/10000999.yaml configs/10000999.yaml.bak

# 恢复配置
cp configs/10000999.yaml.bak configs/10000999.yaml
```

## 🔗 相关文档

- [详细使用文档](README_GAME_GENERATOR.md)
- [API参考](docs/api.md)
- [开发指南](docs/development.md)

## 💡 最佳实践

1. **命名规范**：使用8位数字作为游戏ID
2. **版本控制**：及时提交生成的代码
3. **测试优先**：生成后立即运行测试
4. **配置管理**：保持配置文件的可读性
5. **文档更新**：及时更新游戏说明文档

## 🆘 获取帮助

如果遇到问题：

1. 查看错误信息和日志
2. 检查本文档的常见问题部分
3. 运行测试脚本诊断问题
4. 联系开发团队获取支持

---

**祝您使用愉快！** 🎉
