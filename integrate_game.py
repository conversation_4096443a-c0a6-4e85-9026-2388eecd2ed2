#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏集成脚本
用于将新生成的游戏集成到现有系统中

使用方法:
    python integrate_game.py <gameId>

功能:
- 验证生成的文件是否存在
- 检查代码编译是否正常
- 更新相关配置文件
- 生成集成报告
"""

import os
import sys
import subprocess
from pathlib import Path
from typing import List, Tuple, Optional


class GameIntegrator:
    """游戏集成器"""
    
    def __init__(self, game_id: str):
        self.game_id = game_id
        self.workspace_root = Path("/Users/<USER>/Documents/Project/go/src/igame.github.com/igamexDasino")
        self.modules_dir = self.workspace_root / "modules"
        self.http_games_dir = self.workspace_root / "http" / "games"
        self.configs_dir = self.workspace_root / "configs"
        
    def check_files_exist(self) -> <PERSON><PERSON>[bool, List[str]]:
        """检查必要文件是否存在"""
        required_files = [
            self.modules_dir / f"m{self.game_id}.go",
            self.http_games_dir / f"s{self.game_id}.go",
            self.configs_dir / f"{self.game_id}.yaml"
        ]
        
        missing_files = []
        for file_path in required_files:
            if not file_path.exists():
                missing_files.append(str(file_path))
                
        return len(missing_files) == 0, missing_files
        
    def check_compilation(self) -> Tuple[bool, str]:
        """检查代码编译是否正常"""
        try:
            # 检查模块文件编译
            result = subprocess.run(
                ["go", "build", f"modules/m{self.game_id}.go"],
                cwd=self.workspace_root,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                return False, f"模块文件编译失败:\n{result.stderr}"
                
            # 检查HTTP文件编译
            result = subprocess.run(
                ["go", "build", f"http/games/s{self.game_id}.go"],
                cwd=self.workspace_root,
                capture_output=True,
                text=True,
                timeout=30
            )
            
            if result.returncode != 0:
                return False, f"HTTP文件编译失败:\n{result.stderr}"
                
            return True, "编译检查通过"
            
        except subprocess.TimeoutExpired:
            return False, "编译检查超时"
        except Exception as e:
            return False, f"编译检查出错: {e}"
            
    def validate_config_file(self) -> Tuple[bool, str]:
        """验证配置文件格式"""
        config_file = self.configs_dir / f"{self.game_id}.yaml"
        
        try:
            import yaml
            with open(config_file, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                
            # 检查必要字段
            required_fields = ['GameID', 'Row', 'Column', 'IconWeight']
            missing_fields = []
            
            for field in required_fields:
                if field not in config:
                    missing_fields.append(field)
                    
            if missing_fields:
                return False, f"配置文件缺少必要字段: {', '.join(missing_fields)}"
                
            # 验证GameID是否匹配
            if str(config.get('GameID')) != self.game_id:
                return False, f"配置文件中的GameID ({config.get('GameID')}) 与指定ID ({self.game_id}) 不匹配"
                
            return True, "配置文件验证通过"
            
        except ImportError:
            return False, "缺少yaml模块，请安装: pip install pyyaml"
        except Exception as e:
            return False, f"配置文件验证失败: {e}"
            
    def generate_build_commands(self) -> List[str]:
        """生成构建命令"""
        commands = [
            f"# 游戏 {self.game_id} 构建命令",
            "",
            "# 1. 生成游戏数据",
            f"cd bin && go run ../cmd/generate/main.go {self.game_id}",
            "",
            "# 2. 生成支付表 (CTL 0)",
            f"cd bin && go run ../cmd/paytab/main.go {self.game_id} 0",
            "",
            "# 3. 测试游戏",
            f"go test -bench=BenchmarkSpin -v ./test -args {self.game_id}",
            "",
            "# 4. 模拟测试 (可选)",
            f"cd bin && go run ../cmd/simulate/main.go {self.game_id} 0 0.05 1000000",
            "",
            "# 5. 启动服务测试",
            f"go run cmd/service/main.go {self.game_id}",
        ]
        return commands
        
    def create_integration_script(self) -> bool:
        """创建集成脚本"""
        try:
            script_content = f"""#!/bin/bash
# 游戏 {self.game_id} 集成脚本
# 自动生成于: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

set -e  # 遇到错误立即退出

echo "开始集成游戏 {self.game_id}..."

# 检查文件是否存在
echo "检查必要文件..."
if [ ! -f "modules/m{self.game_id}.go" ]; then
    echo "错误: 模块文件不存在"
    exit 1
fi

if [ ! -f "http/games/s{self.game_id}.go" ]; then
    echo "错误: HTTP文件不存在"
    exit 1
fi

if [ ! -f "configs/{self.game_id}.yaml" ]; then
    echo "错误: 配置文件不存在"
    exit 1
fi

# 编译检查
echo "编译检查..."
go build modules/m{self.game_id}.go
go build http/games/s{self.game_id}.go
echo "编译检查通过"

# 生成游戏数据
echo "生成游戏数据..."
cd bin
go run ../cmd/generate/main.go {self.game_id}
cd ..

# 生成支付表
echo "生成支付表..."
cd bin
go run ../cmd/paytab/main.go {self.game_id} 0
cd ..

# 运行测试
echo "运行测试..."
go test -bench=BenchmarkSpin -timeout 30s -v ./test -args {self.game_id}

echo "游戏 {self.game_id} 集成完成！"
echo ""
echo "下一步操作:"
echo "1. 检查生成的数据文件"
echo "2. 调整配置文件参数"
echo "3. 运行模拟测试"
echo "4. 部署到测试环境"
"""
            
            script_file = self.workspace_root / f"integrate_{self.game_id}.sh"
            with open(script_file, 'w', encoding='utf-8') as f:
                f.write(script_content)
                
            # 设置执行权限
            os.chmod(script_file, 0o755)
            
            print(f"✓ 已生成集成脚本: {script_file}")
            return True
            
        except Exception as e:
            print(f"✗ 生成集成脚本失败: {e}")
            return False
            
    def generate_report(self) -> str:
        """生成集成报告"""
        report = f"""
========================================
游戏 {self.game_id} 集成报告
========================================
生成时间: {__import__('datetime').datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

文件检查:
"""
        
        # 检查文件
        files_ok, missing_files = self.check_files_exist()
        if files_ok:
            report += "✓ 所有必要文件存在\n"
        else:
            report += "✗ 缺少文件:\n"
            for file in missing_files:
                report += f"  - {file}\n"
                
        # 检查编译
        compile_ok, compile_msg = self.check_compilation()
        report += f"\n编译检查:\n"
        if compile_ok:
            report += f"✓ {compile_msg}\n"
        else:
            report += f"✗ {compile_msg}\n"
            
        # 检查配置
        config_ok, config_msg = self.validate_config_file()
        report += f"\n配置验证:\n"
        if config_ok:
            report += f"✓ {config_msg}\n"
        else:
            report += f"✗ {config_msg}\n"
            
        # 构建命令
        report += f"\n推荐的构建命令:\n"
        commands = self.generate_build_commands()
        for cmd in commands:
            report += f"{cmd}\n"
            
        # 总结
        all_ok = files_ok and compile_ok and config_ok
        report += f"\n总结:\n"
        if all_ok:
            report += "✓ 游戏集成准备就绪，可以开始构建流程\n"
        else:
            report += "✗ 存在问题，请先解决上述错误\n"
            
        report += "\n========================================"
        
        return report
        
    def run(self) -> bool:
        """运行集成检查"""
        print(f"开始检查游戏 {self.game_id} 的集成状态...")
        
        # 生成报告
        report = self.generate_report()
        print(report)
        
        # 创建集成脚本
        script_created = self.create_integration_script()
        
        # 检查是否所有检查都通过
        files_ok, _ = self.check_files_exist()
        compile_ok, _ = self.check_compilation()
        config_ok, _ = self.validate_config_file()
        
        return files_ok and compile_ok and config_ok and script_created


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python integrate_game.py <gameId>")
        print("示例: python integrate_game.py 10000999")
        sys.exit(1)
        
    game_id = sys.argv[1]
    
    try:
        integrator = GameIntegrator(game_id)
        success = integrator.run()
        
        if success:
            print("\n🎉 游戏集成检查完成！")
            print(f"可以运行 ./integrate_{game_id}.sh 开始构建流程")
            sys.exit(0)
        else:
            print("\n❌ 游戏集成检查发现问题！")
            print("请先解决上述问题再进行集成")
            sys.exit(1)
            
    except Exception as e:
        print(f"\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
