#!/bin/bash

# 游戏引擎框架生成器构建脚本
# 用于编译和安装gamegen工具

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 项目信息
PROJECT_NAME="gamegen"
VERSION="1.0.0"
BUILD_DIR="build"
SOURCE_DIR="cmd/gamegen"

echo -e "${BLUE}🎮 游戏引擎框架生成器构建脚本${NC}"
echo -e "${BLUE}版本: ${VERSION}${NC}"
echo "=================================="

# 检查Go环境
check_go() {
    if ! command -v go &> /dev/null; then
        echo -e "${RED}❌ Go未安装或不在PATH中${NC}"
        exit 1
    fi
    
    GO_VERSION=$(go version | awk '{print $3}')
    echo -e "${GREEN}✅ Go环境检查通过: ${GO_VERSION}${NC}"
}

# 检查源码目录
check_source() {
    if [ ! -d "$SOURCE_DIR" ]; then
        echo -e "${RED}❌ 源码目录不存在: $SOURCE_DIR${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 源码目录检查通过${NC}"
}

# 创建构建目录
create_build_dir() {
    if [ ! -d "$BUILD_DIR" ]; then
        mkdir -p "$BUILD_DIR"
        echo -e "${GREEN}✅ 创建构建目录: $BUILD_DIR${NC}"
    fi
}

# 清理旧的构建文件
clean_build() {
    if [ "$1" = "clean" ]; then
        echo -e "${YELLOW}🧹 清理旧的构建文件...${NC}"
        rm -rf "$BUILD_DIR"
        mkdir -p "$BUILD_DIR"
    fi
}

# 检查依赖
check_dependencies() {
    echo -e "${BLUE}📦 检查依赖...${NC}"
    
    cd "$SOURCE_DIR"
    
    # 检查go.mod
    if [ ! -f "../../go.mod" ]; then
        echo -e "${YELLOW}⚠️  go.mod不存在，初始化模块...${NC}"
        cd ../..
        go mod init igame.github.com/igamexDasino
        cd "$SOURCE_DIR"
    fi
    
    # 整理依赖
    cd ../..
    go mod tidy
    
    echo -e "${GREEN}✅ 依赖检查完成${NC}"
    cd "$SOURCE_DIR"
}

# 运行测试
run_tests() {
    if [ "$1" != "skip-test" ]; then
        echo -e "${BLUE}🧪 运行测试...${NC}"
        cd ../..
        
        # 检查是否有测试文件
        if find . -name "*_test.go" -type f | grep -q .; then
            go test ./cmd/gamegen/... -v
            echo -e "${GREEN}✅ 测试通过${NC}"
        else
            echo -e "${YELLOW}⚠️  没有找到测试文件，跳过测试${NC}"
        fi
        
        cd "$SOURCE_DIR"
    fi
}

# 构建二进制文件
build_binary() {
    echo -e "${BLUE}🔨 构建二进制文件...${NC}"
    
    # 构建信息
    BUILD_TIME=$(date -u '+%Y-%m-%d %H:%M:%S UTC')
    GIT_COMMIT=$(git rev-parse --short HEAD 2>/dev/null || echo "unknown")
    
    # 构建标志
    LDFLAGS="-X 'main.Version=${VERSION}' -X 'main.BuildTime=${BUILD_TIME}' -X 'main.GitCommit=${GIT_COMMIT}'"
    
    # 构建不同平台的二进制文件
    platforms=("linux/amd64" "darwin/amd64" "darwin/arm64" "windows/amd64")
    
    for platform in "${platforms[@]}"; do
        IFS='/' read -r GOOS GOARCH <<< "$platform"
        
        output_name="$PROJECT_NAME"
        if [ "$GOOS" = "windows" ]; then
            output_name="${output_name}.exe"
        fi
        
        output_path="../../$BUILD_DIR/${GOOS}_${GOARCH}/$output_name"
        
        echo -e "${YELLOW}📦 构建 $GOOS/$GOARCH...${NC}"
        
        mkdir -p "../../$BUILD_DIR/${GOOS}_${GOARCH}"
        
        env GOOS="$GOOS" GOARCH="$GOARCH" go build \
            -ldflags="$LDFLAGS" \
            -o "$output_path" \
            .
        
        if [ $? -eq 0 ]; then
            echo -e "${GREEN}✅ $GOOS/$GOARCH 构建成功: $output_path${NC}"
        else
            echo -e "${RED}❌ $GOOS/$GOARCH 构建失败${NC}"
            exit 1
        fi
    done
}

# 创建本地链接
create_local_link() {
    echo -e "${BLUE}🔗 创建本地可执行文件...${NC}"
    
    # 检测当前系统
    if [[ "$OSTYPE" == "linux-gnu"* ]]; then
        PLATFORM="linux_amd64"
    elif [[ "$OSTYPE" == "darwin"* ]]; then
        if [[ $(uname -m) == "arm64" ]]; then
            PLATFORM="darwin_arm64"
        else
            PLATFORM="darwin_amd64"
        fi
    else
        echo -e "${YELLOW}⚠️  未知系统类型，跳过本地链接创建${NC}"
        return
    fi
    
    SOURCE_BINARY="../../$BUILD_DIR/$PLATFORM/$PROJECT_NAME"
    LOCAL_BINARY="../../$PROJECT_NAME"
    
    if [ -f "$SOURCE_BINARY" ]; then
        cp "$SOURCE_BINARY" "$LOCAL_BINARY"
        chmod +x "$LOCAL_BINARY"
        echo -e "${GREEN}✅ 本地可执行文件创建成功: $LOCAL_BINARY${NC}"
    else
        echo -e "${RED}❌ 源二进制文件不存在: $SOURCE_BINARY${NC}"
    fi
}

# 生成校验和
generate_checksums() {
    echo -e "${BLUE}🔐 生成校验和...${NC}"
    
    cd "../../$BUILD_DIR"
    
    for dir in */; do
        if [ -d "$dir" ]; then
            cd "$dir"
            for file in *; do
                if [ -f "$file" ]; then
                    sha256sum "$file" > "${file}.sha256"
                    echo -e "${GREEN}✅ 生成校验和: ${dir}${file}.sha256${NC}"
                fi
            done
            cd ..
        fi
    done
    
    cd "../$SOURCE_DIR"
}

# 打包发布文件
package_release() {
    if [ "$1" = "package" ]; then
        echo -e "${BLUE}📦 打包发布文件...${NC}"
        
        cd "../../$BUILD_DIR"
        
        for dir in */; do
            if [ -d "$dir" ]; then
                platform_name=${dir%/}
                archive_name="${PROJECT_NAME}_${VERSION}_${platform_name}"
                
                if [[ "$platform_name" == *"windows"* ]]; then
                    zip -r "${archive_name}.zip" "$dir"
                    echo -e "${GREEN}✅ 创建ZIP包: ${archive_name}.zip${NC}"
                else
                    tar -czf "${archive_name}.tar.gz" "$dir"
                    echo -e "${GREEN}✅ 创建TAR包: ${archive_name}.tar.gz${NC}"
                fi
            fi
        done
        
        cd "../$SOURCE_DIR"
    fi
}

# 显示构建结果
show_results() {
    echo ""
    echo -e "${GREEN}🎉 构建完成！${NC}"
    echo "=================================="
    echo -e "${BLUE}构建目录:${NC} $BUILD_DIR"
    echo -e "${BLUE}可用平台:${NC}"
    
    cd "../../$BUILD_DIR"
    for dir in */; do
        if [ -d "$dir" ]; then
            platform_name=${dir%/}
            binary_path="$dir$PROJECT_NAME"
            if [[ "$platform_name" == *"windows"* ]]; then
                binary_path="${binary_path}.exe"
            fi
            
            if [ -f "$binary_path" ]; then
                size=$(du -h "$binary_path" | cut -f1)
                echo -e "  ${GREEN}✅${NC} $platform_name ($size)"
            fi
        fi
    done
    
    cd "../$SOURCE_DIR"
    
    echo ""
    echo -e "${BLUE}使用方法:${NC}"
    echo "  ./gamegen -id=10000999 -type=slot"
    echo "  ./gamegen -id=10000999 -interactive"
    echo ""
    echo -e "${BLUE}文档:${NC} cmd/gamegen/README.md"
}

# 主函数
main() {
    # 解析参数
    CLEAN_BUILD=false
    SKIP_TEST=false
    PACKAGE_RELEASE=false
    
    for arg in "$@"; do
        case $arg in
            clean)
                CLEAN_BUILD=true
                ;;
            skip-test)
                SKIP_TEST=true
                ;;
            package)
                PACKAGE_RELEASE=true
                ;;
        esac
    done
    
    # 执行构建步骤
    check_go
    check_source
    create_build_dir
    
    if [ "$CLEAN_BUILD" = true ]; then
        clean_build clean
    fi
    
    check_dependencies
    
    if [ "$SKIP_TEST" = false ]; then
        run_tests
    fi
    
    build_binary
    create_local_link
    generate_checksums
    
    if [ "$PACKAGE_RELEASE" = true ]; then
        package_release package
    fi
    
    show_results
}

# 显示帮助信息
show_help() {
    echo "游戏引擎框架生成器构建脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  clean      清理旧的构建文件"
    echo "  skip-test  跳过测试"
    echo "  package    打包发布文件"
    echo "  help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0                    # 标准构建"
    echo "  $0 clean              # 清理后构建"
    echo "  $0 skip-test package  # 跳过测试并打包"
}

# 检查帮助参数
if [ "$1" = "help" ] || [ "$1" = "-h" ] || [ "$1" = "--help" ]; then
    show_help
    exit 0
fi

# 运行主函数
main "$@"
