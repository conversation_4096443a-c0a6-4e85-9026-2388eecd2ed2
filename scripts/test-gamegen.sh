#!/bin/bash

# 游戏引擎框架生成器测试脚本
# 用于测试gamegen工具的各种功能

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 测试配置
GAMEGEN_BINARY="./gamegen"
TEST_DIR="test_output"
TEST_GAME_IDS=(10000999 10001000 10001001 10001002)
GAME_TYPES=("slot" "ways" "cascade" "line")

echo -e "${BLUE}🧪 游戏引擎框架生成器测试脚本${NC}"
echo "=================================="

# 检查gamegen二进制文件
check_binary() {
    if [ ! -f "$GAMEGEN_BINARY" ]; then
        echo -e "${RED}❌ gamegen二进制文件不存在: $GAMEGEN_BINARY${NC}"
        echo -e "${YELLOW}请先运行构建脚本: ./scripts/build-gamegen.sh${NC}"
        exit 1
    fi
    
    if [ ! -x "$GAMEGEN_BINARY" ]; then
        echo -e "${RED}❌ gamegen文件不可执行${NC}"
        chmod +x "$GAMEGEN_BINARY"
    fi
    
    echo -e "${GREEN}✅ gamegen二进制文件检查通过${NC}"
}

# 创建测试目录
setup_test_env() {
    echo -e "${BLUE}📁 设置测试环境...${NC}"
    
    if [ -d "$TEST_DIR" ]; then
        rm -rf "$TEST_DIR"
    fi
    
    mkdir -p "$TEST_DIR"/{modules,http/games,configs,backup}
    
    echo -e "${GREEN}✅ 测试环境设置完成${NC}"
}

# 清理测试环境
cleanup_test_env() {
    if [ "$1" = "cleanup" ]; then
        echo -e "${YELLOW}🧹 清理测试环境...${NC}"
        rm -rf "$TEST_DIR"
        
        # 清理可能生成的文件
        for game_id in "${TEST_GAME_IDS[@]}"; do
            rm -f "modules/m${game_id}.go"
            rm -f "http/games/s${game_id}.go"
            rm -f "configs/${game_id}.yaml"
        done
        
        echo -e "${GREEN}✅ 测试环境清理完成${NC}"
    fi
}

# 测试基本功能
test_basic_generation() {
    echo -e "${BLUE}🎮 测试基本代码生成功能...${NC}"
    
    local test_count=0
    local pass_count=0
    
    for i in "${!GAME_TYPES[@]}"; do
        local game_type="${GAME_TYPES[$i]}"
        local game_id="${TEST_GAME_IDS[$i]}"
        
        echo -e "${YELLOW}测试 $game_type 游戏类型 (ID: $game_id)...${NC}"
        
        test_count=$((test_count + 1))
        
        # 运行生成器
        if $GAMEGEN_BINARY -id="$game_id" -type="$game_type" -force -validate=false; then
            # 检查生成的文件
            local files_exist=true
            local expected_files=(
                "modules/m${game_id}.go"
                "http/games/s${game_id}.go"
                "configs/${game_id}.yaml"
            )
            
            for file in "${expected_files[@]}"; do
                if [ ! -f "$file" ]; then
                    echo -e "${RED}❌ 文件未生成: $file${NC}"
                    files_exist=false
                fi
            done
            
            if [ "$files_exist" = true ]; then
                echo -e "${GREEN}✅ $game_type 测试通过${NC}"
                pass_count=$((pass_count + 1))
            else
                echo -e "${RED}❌ $game_type 测试失败: 文件生成不完整${NC}"
            fi
        else
            echo -e "${RED}❌ $game_type 测试失败: 生成器执行失败${NC}"
        fi
        
        echo ""
    done
    
    echo -e "${BLUE}基本功能测试结果: $pass_count/$test_count 通过${NC}"
    return $((test_count - pass_count))
}

# 测试参数验证
test_parameter_validation() {
    echo -e "${BLUE}🔍 测试参数验证...${NC}"
    
    local test_count=0
    local pass_count=0
    
    # 测试无效游戏ID
    echo -e "${YELLOW}测试无效游戏ID...${NC}"
    test_count=$((test_count + 1))
    if ! $GAMEGEN_BINARY -id=0 -type=slot 2>/dev/null; then
        echo -e "${GREEN}✅ 无效游戏ID验证通过${NC}"
        pass_count=$((pass_count + 1))
    else
        echo -e "${RED}❌ 无效游戏ID验证失败${NC}"
    fi
    
    # 测试无效游戏类型
    echo -e "${YELLOW}测试无效游戏类型...${NC}"
    test_count=$((test_count + 1))
    if ! $GAMEGEN_BINARY -id=99999 -type=invalid 2>/dev/null; then
        echo -e "${GREEN}✅ 无效游戏类型验证通过${NC}"
        pass_count=$((pass_count + 1))
    else
        echo -e "${RED}❌ 无效游戏类型验证失败${NC}"
    fi
    
    # 测试无效网格参数
    echo -e "${YELLOW}测试无效网格参数...${NC}"
    test_count=$((test_count + 1))
    if ! $GAMEGEN_BINARY -id=99999 -type=slot -row=0 -column=0 2>/dev/null; then
        echo -e "${GREEN}✅ 无效网格参数验证通过${NC}"
        pass_count=$((pass_count + 1))
    else
        echo -e "${RED}❌ 无效网格参数验证失败${NC}"
    fi
    
    echo -e "${BLUE}参数验证测试结果: $pass_count/$test_count 通过${NC}"
    return $((test_count - pass_count))
}

# 测试文件冲突处理
test_conflict_handling() {
    echo -e "${BLUE}⚔️  测试文件冲突处理...${NC}"
    
    local test_game_id=99998
    local test_count=0
    local pass_count=0
    
    # 先生成一个文件
    echo -e "${YELLOW}创建初始文件...${NC}"
    $GAMEGEN_BINARY -id="$test_game_id" -type=slot -force -validate=false
    
    # 测试非强制模式下的冲突检测
    echo -e "${YELLOW}测试冲突检测...${NC}"
    test_count=$((test_count + 1))
    if ! $GAMEGEN_BINARY -id="$test_game_id" -type=slot -validate=false 2>/dev/null; then
        echo -e "${GREEN}✅ 冲突检测通过${NC}"
        pass_count=$((pass_count + 1))
    else
        echo -e "${RED}❌ 冲突检测失败${NC}"
    fi
    
    # 测试强制覆盖模式
    echo -e "${YELLOW}测试强制覆盖...${NC}"
    test_count=$((test_count + 1))
    if $GAMEGEN_BINARY -id="$test_game_id" -type=ways -force -validate=false; then
        echo -e "${GREEN}✅ 强制覆盖通过${NC}"
        pass_count=$((pass_count + 1))
    else
        echo -e "${RED}❌ 强制覆盖失败${NC}"
    fi
    
    # 清理测试文件
    rm -f "modules/m${test_game_id}.go"
    rm -f "http/games/s${test_game_id}.go"
    rm -f "configs/${test_game_id}.yaml"
    
    echo -e "${BLUE}冲突处理测试结果: $pass_count/$test_count 通过${NC}"
    return $((test_count - pass_count))
}

# 测试代码验证功能
test_code_validation() {
    echo -e "${BLUE}🔍 测试代码验证功能...${NC}"
    
    local test_game_id=99997
    local test_count=0
    local pass_count=0
    
    # 测试代码验证
    echo -e "${YELLOW}测试代码验证...${NC}"
    test_count=$((test_count + 1))
    if $GAMEGEN_BINARY -id="$test_game_id" -type=slot -force -validate=true; then
        echo -e "${GREEN}✅ 代码验证通过${NC}"
        pass_count=$((pass_count + 1))
    else
        echo -e "${RED}❌ 代码验证失败${NC}"
    fi
    
    # 清理测试文件
    rm -f "modules/m${test_game_id}.go"
    rm -f "http/games/s${test_game_id}.go"
    rm -f "configs/${test_game_id}.yaml"
    
    echo -e "${BLUE}代码验证测试结果: $pass_count/$test_count 通过${NC}"
    return $((test_count - pass_count))
}

# 测试不同配置组合
test_configuration_combinations() {
    echo -e "${BLUE}⚙️  测试配置组合...${NC}"
    
    local test_count=0
    local pass_count=0
    
    # 测试配置
    local configs=(
        "slot 3 5 20 250000"
        "ways 4 5 20 500000"
        "cascade 6 6 1 1000000"
        "line 3 5 20 300000"
    )
    
    for config in "${configs[@]}"; do
        read -r type row col lines payout <<< "$config"
        local game_id=$((99990 + test_count))
        
        echo -e "${YELLOW}测试配置: $type ${row}x${col} ${lines}线 最大奖金${payout}...${NC}"
        test_count=$((test_count + 1))
        
        if $GAMEGEN_BINARY -id="$game_id" -type="$type" -row="$row" -column="$col" -lines="$lines" -max-payout="$payout" -force -validate=false; then
            echo -e "${GREEN}✅ 配置测试通过${NC}"
            pass_count=$((pass_count + 1))
            
            # 清理
            rm -f "modules/m${game_id}.go"
            rm -f "http/games/s${game_id}.go"
            rm -f "configs/${game_id}.yaml"
        else
            echo -e "${RED}❌ 配置测试失败${NC}"
        fi
    done
    
    echo -e "${BLUE}配置组合测试结果: $pass_count/$test_count 通过${NC}"
    return $((test_count - pass_count))
}

# 性能测试
test_performance() {
    echo -e "${BLUE}⚡ 性能测试...${NC}"
    
    local start_time=$(date +%s)
    local test_game_id=99996
    
    echo -e "${YELLOW}测试生成速度...${NC}"
    
    # 生成多个文件并计时
    for i in {1..5}; do
        local game_id=$((test_game_id + i))
        $GAMEGEN_BINARY -id="$game_id" -type=slot -force -validate=false > /dev/null
        
        # 清理
        rm -f "modules/m${game_id}.go"
        rm -f "http/games/s${game_id}.go"
        rm -f "configs/${game_id}.yaml"
    done
    
    local end_time=$(date +%s)
    local duration=$((end_time - start_time))
    
    echo -e "${GREEN}✅ 生成5个游戏框架耗时: ${duration}秒${NC}"
    
    if [ $duration -lt 10 ]; then
        echo -e "${GREEN}✅ 性能测试通过 (< 10秒)${NC}"
        return 0
    else
        echo -e "${YELLOW}⚠️  性能测试警告 (>= 10秒)${NC}"
        return 1
    fi
}

# 生成测试报告
generate_report() {
    local total_tests=$1
    local failed_tests=$2
    local passed_tests=$((total_tests - failed_tests))
    
    echo ""
    echo -e "${BLUE}📊 测试报告${NC}"
    echo "=================================="
    echo -e "${GREEN}通过测试: $passed_tests${NC}"
    echo -e "${RED}失败测试: $failed_tests${NC}"
    echo -e "${BLUE}总计测试: $total_tests${NC}"
    
    local success_rate=$((passed_tests * 100 / total_tests))
    echo -e "${BLUE}成功率: ${success_rate}%${NC}"
    
    if [ $failed_tests -eq 0 ]; then
        echo -e "${GREEN}🎉 所有测试通过！${NC}"
        return 0
    else
        echo -e "${RED}❌ 有测试失败${NC}"
        return 1
    fi
}

# 显示帮助信息
show_help() {
    echo "游戏引擎框架生成器测试脚本"
    echo ""
    echo "用法: $0 [选项]"
    echo ""
    echo "选项:"
    echo "  basic      仅运行基本功能测试"
    echo "  validation 仅运行参数验证测试"
    echo "  conflict   仅运行冲突处理测试"
    echo "  code       仅运行代码验证测试"
    echo "  config     仅运行配置组合测试"
    echo "  perf       仅运行性能测试"
    echo "  cleanup    清理测试环境"
    echo "  help       显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0           # 运行所有测试"
    echo "  $0 basic     # 仅运行基本功能测试"
    echo "  $0 cleanup   # 清理测试环境"
}

# 主函数
main() {
    local test_type="$1"
    local total_failed=0
    local total_tests=0
    
    case "$test_type" in
        "help"|"-h"|"--help")
            show_help
            exit 0
            ;;
        "cleanup")
            cleanup_test_env cleanup
            exit 0
            ;;
        "basic")
            check_binary
            setup_test_env
            test_basic_generation
            total_failed=$?
            total_tests=4
            ;;
        "validation")
            check_binary
            test_parameter_validation
            total_failed=$?
            total_tests=3
            ;;
        "conflict")
            check_binary
            test_conflict_handling
            total_failed=$?
            total_tests=2
            ;;
        "code")
            check_binary
            test_code_validation
            total_failed=$?
            total_tests=1
            ;;
        "config")
            check_binary
            test_configuration_combinations
            total_failed=$?
            total_tests=4
            ;;
        "perf")
            check_binary
            test_performance
            total_failed=$?
            total_tests=1
            ;;
        *)
            # 运行所有测试
            check_binary
            setup_test_env
            
            echo -e "${BLUE}🚀 开始完整测试套件...${NC}"
            echo ""
            
            # 基本功能测试
            test_basic_generation
            local basic_failed=$?
            total_failed=$((total_failed + basic_failed))
            total_tests=$((total_tests + 4))
            
            echo ""
            
            # 参数验证测试
            test_parameter_validation
            local validation_failed=$?
            total_failed=$((total_failed + validation_failed))
            total_tests=$((total_tests + 3))
            
            echo ""
            
            # 冲突处理测试
            test_conflict_handling
            local conflict_failed=$?
            total_failed=$((total_failed + conflict_failed))
            total_tests=$((total_tests + 2))
            
            echo ""
            
            # 代码验证测试
            test_code_validation
            local code_failed=$?
            total_failed=$((total_failed + code_failed))
            total_tests=$((total_tests + 1))
            
            echo ""
            
            # 配置组合测试
            test_configuration_combinations
            local config_failed=$?
            total_failed=$((total_failed + config_failed))
            total_tests=$((total_tests + 4))
            
            echo ""
            
            # 性能测试
            test_performance
            local perf_failed=$?
            total_failed=$((total_failed + perf_failed))
            total_tests=$((total_tests + 1))
            ;;
    esac
    
    # 生成报告
    generate_report $total_tests $total_failed
    exit $total_failed
}

# 运行主函数
main "$@"
