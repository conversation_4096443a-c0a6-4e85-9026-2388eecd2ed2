#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
高级游戏引擎框架生成脚本
支持多种游戏类型模板和自定义选项

使用方法:
    python generate_game_advanced.py <gameId> [options]

选项:
    --type <type>        游戏类型 (slot, ways, cascade, poker)
    --lines <number>     线数 (默认: 20)
    --rows <number>      行数 (默认: 4)  
    --columns <number>   列数 (默认: 5)
    --interactive        交互式配置模式
    --template <path>    使用自定义模板文件

示例:
    python generate_game_advanced.py 10000999 --type slot --lines 25
    python generate_game_advanced.py 10000888 --type ways --rows 6 --columns 5
    python generate_game_advanced.py 10000777 --interactive
"""

import os
import sys
import re
import json
import argparse
from pathlib import Path
from typing import Dict, List, Optional, Tuple
from datetime import datetime
from dataclasses import dataclass


@dataclass
class GameConfig:
    """游戏配置数据类"""
    game_id: str
    game_type: str = "slot"
    lines: int = 20
    rows: int = 4
    columns: int = 5
    max_payout: int = 250000
    wild_icon: int = 9
    scatter_icon: int = 10
    free_spin_trigger: int = 3
    free_spin_count: int = 10
    cascade_enabled: bool = False
    multiplier_enabled: bool = False


class GameTemplateManager:
    """游戏模板管理器"""
    
    def __init__(self):
        self.templates = {
            'slot': self._get_slot_template,
            'ways': self._get_ways_template,
            'cascade': self._get_cascade_template,
            'poker': self._get_poker_template
        }
        
    def get_template(self, game_type: str, config: GameConfig) -> Tuple[str, str, str]:
        """获取指定类型的模板"""
        if game_type not in self.templates:
            raise ValueError(f"不支持的游戏类型: {game_type}")
        return self.templates[game_type](config)
        
    def _get_slot_template(self, config: GameConfig) -> Tuple[str, str, str]:
        """获取老虎机模板"""
        module_template = f'''package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m{config.game_id}])

type m{config.game_id} struct {{
	Config c{config.game_id}
	RandByWeight *utils.RandomWeightPicker[int16, int]
}}

type c{config.game_id} struct {{
	GameID      int                 `yaml:"GameID"`
	RuleID      int                 `yaml:"RuleID"`
	Row         int32               `yaml:"Row"`
	Column      int32               `yaml:"Column"`
	MaxPayout   int32               `yaml:"MaxPayout"`
	PayoutTable [][]int32           `yaml:"PayoutTable"`
	IconWeight  map[int16]int       `yaml:"IconWeight"`
	WildIcon    int16               `yaml:"WildIcon"`
	ScatterIcon int16               `yaml:"ScatterIcon"`
	Pattern     [][]basic.Position  `yaml:"Pattern"`
}}

func (m m{config.game_id}) ID() int32 {{
	return {config.game_id}
}}

func (m *m{config.game_id}) Line() int32 {{
	return {config.lines}
}}

func (m m{config.game_id}) ClientMode() int32 {{
	return basic.EnumClientMode.ONE
}}

func (m m{config.game_id}) Exception(code int32) string {{
	return games.S{config.game_id}{{}}.Exception(code)
}}

func (m *m{config.game_id}) Init(config []byte) {{
	m.Config = utils.ParseYAML[c{config.game_id}](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}}

func (m m{config.game_id}) Rule() string {{
	b, _ := json.Marshal(m.Config)
	return string(b)
}}

func (m m{config.game_id}) InputCoef(ctl int32) int32 {{
	return 100
}}

func (m m{config.game_id}) MinPayout(ctl int32) int32 {{
	return 0
}}

// ZeroSpin 生成一个不中奖的旋转
func (m *m{config.game_id}) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {{
	for {{
		spin := m.Spin(rand.New(rd))
		if spin.Payout() == 0 {{
			return spin
		}}
	}}
}}

// Spin 执行旋转逻辑 - 传统老虎机模式
func (m *m{config.game_id}) Spin(rd *rand.Rand) basic.ISpin {{
	spin := &games.S{config.game_id}{{
		GameId: m.ID(),
		Line:   m.Line(),
		Row:    m.Config.Row,
		Column: m.Config.Column,
		Pages:  []games.P{config.game_id}{{}},
	}}
	
	// 生成随机网格
	grid := m.generateGrid(rd)
	
	// 计算连线中奖
	lines, payout := m.calculateLineWins(grid)
	
	page := games.P{config.game_id}{{
		Grid:  grid,
		Lines: lines,
		Pay:   payout,
	}}
	
	spin.Pages = append(spin.Pages, page)
	spin.Pays = payout
	
	return spin
}}

// Salting 对旋转结果进行调整
func (m *m{config.game_id}) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {{
	return spin
}}

// generateGrid 生成随机网格
func (m *m{config.game_id}) generateGrid(rd *rand.Rand) basic.Grid {{
	gridSize := int(m.Config.Row * m.Config.Column)
	grid := make(basic.Grid, gridSize)
	
	for i := 0; i < gridSize; i++ {{
		grid[i] = m.RandByWeight.Pick(rd)
	}}
	
	return grid
}}

// calculateLineWins 计算连线中奖 - 传统老虎机连线模式
func (m *m{config.game_id}) calculateLineWins(grid basic.Grid) ([]games.L{config.game_id}, int32) {{
	lines := []games.L{config.game_id}{{}}
	var totalPayout int32 = 0
	
	// 遍历每条支付线
	for lineIndex, pattern := range m.Config.Pattern {{
		if lineIndex >= int(m.Line()) {{
			break
		}}
		
		// 检查连线
		if line, payout := m.checkLine(grid, pattern); payout > 0 {{
			line.LineIndex = int32(lineIndex)
			lines = append(lines, line)
			totalPayout += payout
		}}
	}}
	
	return lines, totalPayout
}}

// checkLine 检查单条线的中奖情况
func (m *m{config.game_id}) checkLine(grid basic.Grid, pattern []basic.Position) (games.L{config.game_id}, int32) {{
	// TODO: 实现具体的连线检查逻辑
	return games.L{config.game_id}{{}}, 0
}}
'''

        http_template = f'''package games

import (
	"encoding/json"
	"igameCommon/basic"
	"time"
)

type S{config.game_id} struct {{
	GameId int32           `json:"gameId"`
	Line   int32           `json:"line"`
	Row    int32           `json:"row"`
	Column int32           `json:"column"`
	Pays   int32           `json:"pays"`
	Pages  []P{config.game_id} `json:"pages"`
}}

type P{config.game_id} struct {{
	Grid  basic.Grid    `json:"grid"`
	Lines []L{config.game_id} `json:"lines"`
	Pay   int32         `json:"pay"`
}}

type L{config.game_id} struct {{
	Icon      int16             `json:"icon"`
	Count     int32             `json:"count"`
	Value     int32             `json:"value"`
	Position  []basic.Position  `json:"position"`
	LineIndex int32             `json:"lineIndex"`
}}

func (s S{config.game_id}) Payout() int32 {{
	return s.Pays
}}

func (s S{config.game_id}) RoundPayouts() []int32 {{
	return []int32{{s.Pays}}
}}

func (s S{config.game_id}) Data(ctx basic.SpinContext) string {{
	lv := ctx.Int32("lv")
	lineCount := s.Line
	gamble := ctx.Float64("gamble")
	balance := ctx.Float64("balance")
	input := ctx.Float64("input")
	
	balance -= input
	inputLine := input / float64(lineCount)
	
	var curReward float64 = float64(s.Pays) * inputLine / 100
	var output float64 = curReward
	
	result := map[string]interface{}{{
		"gameId":    s.GameId,
		"line":      lineCount,
		"balance":   balance + output,
		"input":     input,
		"inputLine": inputLine,
		"output":    output,
		"reward":    curReward,
		"gamble":    gamble,
		"lv":        lv,
		"pages":     s.Pages,
		"timestamp": time.Now().Unix(),
	}}
	
	jsonData, _ := json.Marshal(result)
	return string(jsonData)
}}

func (s S{config.game_id}) RoundData(index int32, ctx basic.SpinContext) string {{
	return s.Data(ctx)
}}

func (s S{config.game_id}) Exception(code int32) string {{
	switch code {{
	case 1001:
		return `{{"error": "insufficient_balance", "message": "余额不足"}}`
	case 1002:
		return `{{"error": "invalid_bet", "message": "投注金额无效"}}`
	case 1003:
		return `{{"error": "game_maintenance", "message": "游戏维护中"}}`
	default:
		return `{{"error": "unknown_error", "message": "未知错误"}}`
	}}
}}

func (s S{config.game_id}) Tag() string {{
	if s.Pays > 0 {{
		return "WIN"
	}}
	return "LOSE"
}}

func (s S{config.game_id}) Tags() []string {{
	tags := []string{{s.Tag()}}
	
	if s.Pays > 1000 {{
		tags = append(tags, "BIG_WIN")
	}}
	if s.Pays > 5000 {{
		tags = append(tags, "MEGA_WIN")
	}}
	
	return tags
}}
'''

        config_template = f'''# 传统老虎机游戏 {config.game_id} 配置文件
# 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

GameID: {config.game_id}
RuleID: 1
Row: {config.rows}
Column: {config.columns}
MaxPayout: {config.max_payout}

# 支付表配置 [图标ID, 连线数, 倍率]
PayoutTable:
  # 高价值符号
  - [8, 3, 50]
  - [8, 4, 200]
  - [8, 5, 500]
  
  - [7, 3, 30]
  - [7, 4, 120]
  - [7, 5, 300]
  
  # 中等价值符号
  - [6, 3, 20]
  - [6, 4, 80]
  - [6, 5, 200]
  
  - [5, 3, 15]
  - [5, 4, 60]
  - [5, 5, 150]
  
  # 低价值符号
  - [4, 3, 10]
  - [4, 4, 40]
  - [4, 5, 100]
  
  - [3, 3, 8]
  - [3, 4, 30]
  - [3, 5, 80]
  
  - [2, 3, 5]
  - [2, 4, 20]
  - [2, 5, 50]
  
  - [1, 3, 3]
  - [1, 4, 15]
  - [1, 5, 30]

# 图标权重配置
IconWeight:
  1: 150
  2: 150
  3: 120
  4: 120
  5: 100
  6: 100
  7: 80
  8: 80
  9: 30     # Wild
  10: 20    # Scatter

WildIcon: {config.wild_icon}
ScatterIcon: {config.scatter_icon}

# 支付线模式 (传统老虎机连线)
Pattern:
  # 第1条线: 中间行
  - [{{x: 0, y: 1}}, {{x: 1, y: 1}}, {{x: 2, y: 1}}, {{x: 3, y: 1}}, {{x: 4, y: 1}}]
  # 第2条线: 上行
  - [{{x: 0, y: 0}}, {{x: 1, y: 0}}, {{x: 2, y: 0}}, {{x: 3, y: 0}}, {{x: 4, y: 0}}]
  # 第3条线: 下行
  - [{{x: 0, y: 2}}, {{x: 1, y: 2}}, {{x: 2, y: 2}}, {{x: 3, y: 2}}, {{x: 4, y: 2}}]
  # 更多支付线...
'''

        return module_template, http_template, config_template

    def _get_ways_template(self, config: GameConfig) -> Tuple[str, str, str]:
        """获取Ways玩法模板"""
        module_template = f'''package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m{config.game_id}])

type m{config.game_id} struct {{
	Config c{config.game_id}
	RandByWeight *utils.RandomWeightPicker[int16, int]
}}

type c{config.game_id} struct {{
	GameID      int                 `yaml:"GameID"`
	RuleID      int                 `yaml:"RuleID"`
	Row         int32               `yaml:"Row"`
	Column      int32               `yaml:"Column"`
	MaxPayout   int32               `yaml:"MaxPayout"`
	PayoutTable [][]int32           `yaml:"PayoutTable"`
	IconWeight  map[int16]int       `yaml:"IconWeight"`
	WildIcon    int16               `yaml:"WildIcon"`
	ScatterIcon int16               `yaml:"ScatterIcon"`
}}

func (m m{config.game_id}) ID() int32 {{
	return {config.game_id}
}}

func (m *m{config.game_id}) Line() int32 {{
	return {config.lines} // Ways玩法的Ways数
}}

func (m m{config.game_id}) ClientMode() int32 {{
	return basic.EnumClientMode.ONE
}}

func (m m{config.game_id}) Exception(code int32) string {{
	return games.S{config.game_id}{{}}.Exception(code)
}}

func (m *m{config.game_id}) Init(config []byte) {{
	m.Config = utils.ParseYAML[c{config.game_id}](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}}

func (m m{config.game_id}) Rule() string {{
	b, _ := json.Marshal(m.Config)
	return string(b)
}}

func (m m{config.game_id}) InputCoef(ctl int32) int32 {{
	return 100
}}

func (m m{config.game_id}) MinPayout(ctl int32) int32 {{
	return 0
}}

// ZeroSpin 生成一个不中奖的旋转
func (m *m{config.game_id}) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {{
	for {{
		spin := m.Spin(rand.New(rd))
		if spin.Payout() == 0 {{
			return spin
		}}
	}}
}}

// Spin 执行旋转逻辑 - Ways玩法
func (m *m{config.game_id}) Spin(rd *rand.Rand) basic.ISpin {{
	spin := &games.S{config.game_id}{{
		GameId: m.ID(),
		Line:   m.Line(),
		Row:    m.Config.Row,
		Column: m.Config.Column,
		Pages:  []games.P{config.game_id}{{}},
	}}

	// 生成随机网格
	grid := m.generateGrid(rd)

	// 计算Ways中奖
	lines, payout := m.calculateWaysWins(grid)

	page := games.P{config.game_id}{{
		Grid:  grid,
		Lines: lines,
		Pay:   payout,
	}}

	spin.Pages = append(spin.Pages, page)
	spin.Pays = payout

	return spin
}}

// Salting 对旋转结果进行调整
func (m *m{config.game_id}) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {{
	return spin
}}

// generateGrid 生成随机网格
func (m *m{config.game_id}) generateGrid(rd *rand.Rand) basic.Grid {{
	gridSize := int(m.Config.Row * m.Config.Column)
	grid := make(basic.Grid, gridSize)

	for i := 0; i < gridSize; i++ {{
		grid[i] = m.RandByWeight.Pick(rd)
	}}

	return grid
}}

// calculateWaysWins 计算Ways中奖
func (m *m{config.game_id}) calculateWaysWins(grid basic.Grid) ([]games.L{config.game_id}, int32) {{
	lines := []games.L{config.game_id}{{}}
	var totalPayout int32 = 0

	// Ways玩法：从左到右连续的相同符号
	for icon := int16(1); icon <= 10; icon++ {{
		if ways, positions := m.calculateIconWays(grid, icon); ways > 0 {{
			// 根据Ways数和符号类型计算奖金
			payout := m.getPayoutForWays(icon, len(positions))
			if payout > 0 {{
				line := games.L{config.game_id}{{
					Icon:     icon,
					Count:    int32(len(positions)),
					Value:    payout,
					Position: positions,
					Ways:     int32(ways),
				}}
				lines = append(lines, line)
				totalPayout += payout
			}}
		}}
	}}

	return lines, totalPayout
}}

// calculateIconWays 计算指定符号的Ways数
func (m *m{config.game_id}) calculateIconWays(grid basic.Grid, icon int16) (int, []basic.Position) {{
	// TODO: 实现Ways计算逻辑
	return 0, []basic.Position{{}}
}}

// getPayoutForWays 根据符号和连线数获取奖金
func (m *m{config.game_id}) getPayoutForWays(icon int16, count int) int32 {{
	// TODO: 根据支付表计算奖金
	return 0
}}
'''

        http_template = f'''package games

import (
	"encoding/json"
	"igameCommon/basic"
	"time"
)

type S{config.game_id} struct {{
	GameId int32           `json:"gameId"`
	Line   int32           `json:"line"`
	Row    int32           `json:"row"`
	Column int32           `json:"column"`
	Pays   int32           `json:"pays"`
	Pages  []P{config.game_id} `json:"pages"`
}}

type P{config.game_id} struct {{
	Grid  basic.Grid    `json:"grid"`
	Lines []L{config.game_id} `json:"lines"`
	Pay   int32         `json:"pay"`
}}

type L{config.game_id} struct {{
	Icon     int16             `json:"icon"`
	Count    int32             `json:"count"`
	Value    int32             `json:"value"`
	Position []basic.Position  `json:"position"`
	Ways     int32             `json:"ways"`
}}

func (s S{config.game_id}) Payout() int32 {{
	return s.Pays
}}

func (s S{config.game_id}) RoundPayouts() []int32 {{
	return []int32{{s.Pays}}
}}

func (s S{config.game_id}) Data(ctx basic.SpinContext) string {{
	lv := ctx.Int32("lv")
	waysCount := s.Line
	gamble := ctx.Float64("gamble")
	balance := ctx.Float64("balance")
	input := ctx.Float64("input")

	balance -= input
	inputWays := input / float64(waysCount)

	var curReward float64 = float64(s.Pays) * inputWays / 100
	var output float64 = curReward

	result := map[string]interface{}{{
		"gameId":    s.GameId,
		"ways":      waysCount,
		"balance":   balance + output,
		"input":     input,
		"inputWays": inputWays,
		"output":    output,
		"reward":    curReward,
		"gamble":    gamble,
		"lv":        lv,
		"pages":     s.Pages,
		"timestamp": time.Now().Unix(),
	}}

	jsonData, _ := json.Marshal(result)
	return string(jsonData)
}}

func (s S{config.game_id}) RoundData(index int32, ctx basic.SpinContext) string {{
	return s.Data(ctx)
}}

func (s S{config.game_id}) Exception(code int32) string {{
	switch code {{
	case 1001:
		return `{{"error": "insufficient_balance", "message": "余额不足"}}`
	case 1002:
		return `{{"error": "invalid_bet", "message": "投注金额无效"}}`
	case 1003:
		return `{{"error": "game_maintenance", "message": "游戏维护中"}}`
	default:
		return `{{"error": "unknown_error", "message": "未知错误"}}`
	}}
}}

func (s S{config.game_id}) Tag() string {{
	if s.Pays > 0 {{
		return "WIN"
	}}
	return "LOSE"
}}

func (s S{config.game_id}) Tags() []string {{
	tags := []string{{s.Tag()}}

	if s.Pays > 1000 {{
		tags = append(tags, "BIG_WIN")
	}}
	if s.Pays > 5000 {{
		tags = append(tags, "MEGA_WIN")
	}}

	return tags
}}
'''

        config_template = f'''# Ways玩法游戏 {config.game_id} 配置文件
# 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

GameID: {config.game_id}
RuleID: 1
Row: {config.rows}
Column: {config.columns}
MaxPayout: {config.max_payout}

# Ways玩法支付表 [图标ID, 连线数, 倍率]
PayoutTable:
  # 高价值符号
  - [8, 3, 100]   # 3连：100x
  - [8, 4, 500]   # 4连：500x
  - [8, 5, 2000]  # 5连：2000x

  - [7, 3, 50]    # 3连：50x
  - [7, 4, 250]   # 4连：250x
  - [7, 5, 1000]  # 5连：1000x

  # 中等价值符号
  - [6, 3, 30]    # 3连：30x
  - [6, 4, 150]   # 4连：150x
  - [6, 5, 600]   # 5连：600x

  - [5, 3, 20]    # 3连：20x
  - [5, 4, 100]   # 4连：100x
  - [5, 5, 400]   # 5连：400x

  # 低价值符号
  - [4, 3, 15]    # 3连：15x
  - [4, 4, 75]    # 4连：75x
  - [4, 5, 300]   # 5连：300x

  - [3, 3, 10]    # 3连：10x
  - [3, 4, 50]    # 4连：50x
  - [3, 5, 200]   # 5连：200x

  - [2, 3, 8]     # 3连：8x
  - [2, 4, 40]    # 4连：40x
  - [2, 5, 160]   # 5连：160x

  - [1, 3, 5]     # 3连：5x
  - [1, 4, 25]    # 4连：25x
  - [1, 5, 100]   # 5连：100x

# 图标权重配置
IconWeight:
  1: 120    # 低价值符号1
  2: 120    # 低价值符号2
  3: 100    # 低价值符号3
  4: 100    # 低价值符号4
  5: 80     # 中等价值符号5
  6: 80     # 中等价值符号6
  7: 60     # 高价值符号7
  8: 60     # 高价值符号8
  9: 25     # Wild符号
  10: 15    # Scatter符号

WildIcon: {config.wild_icon}
ScatterIcon: {config.scatter_icon}

# Ways玩法特殊配置
WaysConfig:
  MinConnected: 3      # 最少连线数
  LeftToRight: true    # 从左到右计算
  WildSubstitute: true # Wild可替代其他符号
'''

        return module_template, http_template, config_template

    def _get_cascade_template(self, config: GameConfig) -> Tuple[str, str, str]:
        """获取级联消除模板"""
        # 这里可以添加级联消除的模板代码
        # 为了简化，暂时返回基础模板
        return self._get_ways_template(config)

    def _get_poker_template(self, config: GameConfig) -> Tuple[str, str, str]:
        """获取扑克游戏模板"""
        # 这里可以添加扑克游戏的模板代码
        # 为了简化，暂时返回基础模板
        return self._get_slot_template(config)


class AdvancedGameGenerator:
    """高级游戏代码生成器"""

    def __init__(self, config: GameConfig):
        self.config = config
        self.workspace_root = Path("/Users/<USER>/Documents/Project/go/src/igame.github.com/igamexDasino")
        self.modules_dir = self.workspace_root / "modules"
        self.http_games_dir = self.workspace_root / "http" / "games"
        self.configs_dir = self.workspace_root / "configs"
        self.template_manager = GameTemplateManager()

        # 确保目录存在
        self.configs_dir.mkdir(exist_ok=True)

    def validate_game_id(self) -> bool:
        """验证游戏ID是否已存在"""
        module_file = self.modules_dir / f"m{self.config.game_id}.go"
        http_file = self.http_games_dir / f"s{self.config.game_id}.go"

        if module_file.exists() or http_file.exists():
            print(f"警告: 游戏ID {self.config.game_id} 已存在相关文件")
            response = input("是否覆盖现有文件? (y/N): ")
            return response.lower() == 'y'
        return True

    def create_files(self) -> bool:
        """创建所有文件"""
        try:
            # 获取模板
            module_content, http_content, config_content = self.template_manager.get_template(
                self.config.game_type, self.config
            )

            # 生成模块文件
            module_file = self.modules_dir / f"m{self.config.game_id}.go"
            with open(module_file, 'w', encoding='utf-8') as f:
                f.write(module_content)
            print(f"✓ 已生成模块文件: {module_file}")

            # 生成HTTP文件
            http_file = self.http_games_dir / f"s{self.config.game_id}.go"
            with open(http_file, 'w', encoding='utf-8') as f:
                f.write(http_content)
            print(f"✓ 已生成HTTP文件: {http_file}")

            # 生成配置文件
            config_file = self.configs_dir / f"{self.config.game_id}.yaml"
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            print(f"✓ 已生成配置文件: {config_file}")

            return True

        except Exception as e:
            print(f"✗ 创建文件时出错: {e}")
            return False

    def generate_usage_instructions(self) -> str:
        """生成使用说明"""
        instructions = f"""
========================================
{self.config.game_type.upper()}类型游戏 {self.config.game_id} 框架生成完成！
========================================

游戏配置:
- 游戏类型: {self.config.game_type}
- 线数/Ways: {self.config.lines}
- 网格大小: {self.config.rows}x{self.config.columns}
- 最大奖金: {self.config.max_payout}

已生成的文件:
1. modules/m{self.config.game_id}.go     - 游戏模块文件
2. http/games/s{self.config.game_id}.go  - HTTP游戏处理文件
3. configs/{self.config.game_id}.yaml    - 配置文件

下一步操作:
1. 根据具体游戏需求修改配置文件 configs/{self.config.game_id}.yaml
2. 实现游戏逻辑:
   - 完善 calculateWin/calculateWaysWins 方法
   - 根据游戏类型实现特定逻辑
   - 添加特殊功能（免费旋转、级联等）

3. 生成游戏数据:
   cd bin && go run ../cmd/generate/main.go {self.config.game_id}

4. 生成支付表:
   cd bin && go run ../cmd/paytab/main.go {self.config.game_id} 0

5. 测试游戏:
   go test -bench=BenchmarkSpin -v ./test -args {self.config.game_id}

特定于{self.config.game_type}类型的注意事项:
""" + self._get_type_specific_notes() + """

========================================
"""
        return instructions

    def _get_type_specific_notes(self) -> str:
        """获取特定游戏类型的注意事项"""
        notes = {
            'slot': "- 需要配置支付线模式(Pattern)\n- 实现传统连线检查逻辑\n- 支持多条支付线同时中奖",
            'ways': "- 实现Ways计算逻辑\n- 符号必须从左到右连续\n- Ways数 = 各列符号数相乘",
            'cascade': "- 实现级联消除逻辑\n- 中奖符号消除后上方符号下落\n- 支持连续级联和乘数递增",
            'poker': "- 实现扑克牌型判断\n- 配置各种牌型的奖金\n- 支持换牌功能"
        }
        return notes.get(self.config.game_type, "- 根据具体游戏类型实现相应逻辑")

    def run(self) -> bool:
        """运行生成器"""
        print(f"开始生成{self.config.game_type}类型游戏 {self.config.game_id} 的框架...")

        # 验证游戏ID
        if not self.validate_game_id():
            print("操作已取消")
            return False

        # 创建文件
        print("生成文件...")
        if not self.create_files():
            return False

        # 显示使用说明
        print(self.generate_usage_instructions())

        return True


def interactive_config() -> GameConfig:
    """交互式配置"""
    print("=== 交互式游戏配置 ===")

    game_id = input("请输入游戏ID: ")
    if not re.match(r'^\d+$', game_id):
        raise ValueError("游戏ID必须是数字")

    print("\\n可用的游戏类型:")
    print("1. slot - 传统老虎机")
    print("2. ways - Ways玩法")
    print("3. cascade - 级联消除")
    print("4. poker - 扑克游戏")

    type_choice = input("\\n请选择游戏类型 (1-4): ")
    type_map = {'1': 'slot', '2': 'ways', '3': 'cascade', '4': 'poker'}
    game_type = type_map.get(type_choice, 'slot')

    lines = int(input(f"请输入线数/Ways数 (默认20): ") or "20")
    rows = int(input(f"请输入行数 (默认4): ") or "4")
    columns = int(input(f"请输入列数 (默认5): ") or "5")
    max_payout = int(input(f"请输入最大奖金 (默认250000): ") or "250000")

    return GameConfig(
        game_id=game_id,
        game_type=game_type,
        lines=lines,
        rows=rows,
        columns=columns,
        max_payout=max_payout
    )


def main():
    """主函数"""
    parser = argparse.ArgumentParser(
        description="高级游戏引擎框架生成脚本",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
示例:
  python generate_game_advanced.py 10000999 --type slot --lines 25
  python generate_game_advanced.py 10000888 --type ways --rows 6 --columns 5
  python generate_game_advanced.py 10000777 --interactive
        """
    )

    parser.add_argument('game_id', nargs='?', help='游戏ID (数字)')
    parser.add_argument('--type', choices=['slot', 'ways', 'cascade', 'poker'],
                       default='slot', help='游戏类型 (默认: slot)')
    parser.add_argument('--lines', type=int, default=20, help='线数/Ways数 (默认: 20)')
    parser.add_argument('--rows', type=int, default=4, help='行数 (默认: 4)')
    parser.add_argument('--columns', type=int, default=5, help='列数 (默认: 5)')
    parser.add_argument('--max-payout', type=int, default=250000, help='最大奖金 (默认: 250000)')
    parser.add_argument('--wild-icon', type=int, default=9, help='Wild图标ID (默认: 9)')
    parser.add_argument('--scatter-icon', type=int, default=10, help='Scatter图标ID (默认: 10)')
    parser.add_argument('--interactive', action='store_true', help='交互式配置模式')
    parser.add_argument('--template', help='使用自定义模板文件')

    args = parser.parse_args()

    try:
        # 交互式模式
        if args.interactive:
            config = interactive_config()
        else:
            # 命令行模式
            if not args.game_id:
                parser.error("需要提供游戏ID或使用 --interactive 模式")

            if not re.match(r'^\d+$', args.game_id):
                parser.error("游戏ID必须是数字")

            config = GameConfig(
                game_id=args.game_id,
                game_type=args.type,
                lines=args.lines,
                rows=args.rows,
                columns=args.columns,
                max_payout=args.max_payout,
                wild_icon=args.wild_icon,
                scatter_icon=args.scatter_icon
            )

        # 显示配置信息
        print(f"\\n=== 游戏配置 ===")
        print(f"游戏ID: {config.game_id}")
        print(f"游戏类型: {config.game_type}")
        print(f"线数/Ways: {config.lines}")
        print(f"网格大小: {config.rows}x{config.columns}")
        print(f"最大奖金: {config.max_payout}")
        print(f"Wild图标: {config.wild_icon}")
        print(f"Scatter图标: {config.scatter_icon}")

        # 确认生成
        if not args.interactive:
            response = input("\\n确认生成? (Y/n): ")
            if response.lower() == 'n':
                print("操作已取消")
                sys.exit(0)

        # 生成游戏框架
        generator = AdvancedGameGenerator(config)
        success = generator.run()

        if success:
            print("\\n🎉 游戏框架生成成功！")
            sys.exit(0)
        else:
            print("\\n❌ 游戏框架生成失败！")
            sys.exit(1)

    except Exception as e:
        print(f"\\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
