#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏生成器测试脚本
用于测试游戏代码生成器的功能

使用方法:
    python test_generator.py

功能:
- 测试基础生成器
- 测试高级生成器
- 验证生成的代码
- 清理测试文件
"""

import os
import sys
import tempfile
import shutil
from pathlib import Path
import subprocess


class GeneratorTester:
    """生成器测试器"""
    
    def __init__(self):
        self.test_game_id = "99999999"  # 测试用游戏ID
        self.workspace_root = Path("/Users/<USER>/Documents/Project/go/src/igame.github.com/igamexDasino")
        self.test_files = []
        
    def setup_test(self):
        """设置测试环境"""
        print("设置测试环境...")
        self.test_files = [
            self.workspace_root / "modules" / f"m{self.test_game_id}.go",
            self.workspace_root / "http" / "games" / f"s{self.test_game_id}.go",
            self.workspace_root / "configs" / f"{self.test_game_id}.yaml",
            self.workspace_root / f"integrate_{self.test_game_id}.sh"
        ]
        
        # 清理可能存在的测试文件
        self.cleanup_test()
        
    def cleanup_test(self):
        """清理测试文件"""
        print("清理测试文件...")
        for file_path in self.test_files:
            if file_path.exists():
                file_path.unlink()
                print(f"删除: {file_path}")
                
    def test_basic_generator(self) -> bool:
        """测试基础生成器"""
        print("\n=== 测试基础生成器 ===")
        
        try:
            # 运行基础生成器
            result = subprocess.run([
                sys.executable, "generate_game.py", self.test_game_id
            ], input="y\n", text=True, capture_output=True, timeout=30)
            
            if result.returncode != 0:
                print(f"基础生成器执行失败: {result.stderr}")
                return False
                
            # 检查生成的文件
            expected_files = self.test_files[:3]  # 前3个文件
            for file_path in expected_files:
                if not file_path.exists():
                    print(f"缺少文件: {file_path}")
                    return False
                    
            print("✓ 基础生成器测试通过")
            return True
            
        except subprocess.TimeoutExpired:
            print("基础生成器测试超时")
            return False
        except Exception as e:
            print(f"基础生成器测试出错: {e}")
            return False
            
    def test_advanced_generator(self) -> bool:
        """测试高级生成器"""
        print("\n=== 测试高级生成器 ===")
        
        # 先清理之前的文件
        self.cleanup_test()
        
        test_cases = [
            {
                "name": "Slot类型",
                "args": [self.test_game_id, "--type", "slot", "--lines", "25"]
            },
            {
                "name": "Ways类型", 
                "args": [self.test_game_id, "--type", "ways", "--rows", "6"]
            }
        ]
        
        for case in test_cases:
            print(f"\n测试 {case['name']}...")
            
            try:
                # 运行高级生成器
                result = subprocess.run([
                    sys.executable, "generate_game_advanced.py"
                ] + case["args"], input="Y\n", text=True, capture_output=True, timeout=30)
                
                if result.returncode != 0:
                    print(f"{case['name']} 生成失败: {result.stderr}")
                    return False
                    
                # 检查生成的文件
                expected_files = self.test_files[:3]
                for file_path in expected_files:
                    if not file_path.exists():
                        print(f"缺少文件: {file_path}")
                        return False
                        
                print(f"✓ {case['name']} 测试通过")
                
                # 清理文件准备下一个测试
                if case != test_cases[-1]:  # 不是最后一个测试
                    self.cleanup_test()
                    
            except subprocess.TimeoutExpired:
                print(f"{case['name']} 测试超时")
                return False
            except Exception as e:
                print(f"{case['name']} 测试出错: {e}")
                return False
                
        return True
        
    def test_integration_script(self) -> bool:
        """测试集成脚本"""
        print("\n=== 测试集成脚本 ===")
        
        try:
            # 运行集成脚本
            result = subprocess.run([
                sys.executable, "integrate_game.py", self.test_game_id
            ], capture_output=True, text=True, timeout=30)
            
            if result.returncode != 0:
                print(f"集成脚本执行失败: {result.stderr}")
                return False
                
            # 检查是否生成了集成脚本
            integration_script = self.workspace_root / f"integrate_{self.test_game_id}.sh"
            if not integration_script.exists():
                print("集成脚本未生成")
                return False
                
            print("✓ 集成脚本测试通过")
            return True
            
        except subprocess.TimeoutExpired:
            print("集成脚本测试超时")
            return False
        except Exception as e:
            print(f"集成脚本测试出错: {e}")
            return False
            
    def test_file_content(self) -> bool:
        """测试生成文件的内容"""
        print("\n=== 测试文件内容 ===")
        
        try:
            # 检查模块文件
            module_file = self.workspace_root / "modules" / f"m{self.test_game_id}.go"
            if module_file.exists():
                content = module_file.read_text(encoding='utf-8')
                
                # 检查关键内容
                required_content = [
                    f"type m{self.test_game_id} struct",
                    f"func (m m{self.test_game_id}) ID() int32",
                    f"return {self.test_game_id}",
                    "Factory.reg"
                ]
                
                for required in required_content:
                    if required not in content:
                        print(f"模块文件缺少内容: {required}")
                        return False
                        
            # 检查HTTP文件
            http_file = self.workspace_root / "http" / "games" / f"s{self.test_game_id}.go"
            if http_file.exists():
                content = http_file.read_text(encoding='utf-8')
                
                required_content = [
                    f"type S{self.test_game_id} struct",
                    "func (s S",
                    "Payout() int32",
                    "Data(ctx basic.SpinContext) string"
                ]
                
                for required in required_content:
                    if required not in content:
                        print(f"HTTP文件缺少内容: {required}")
                        return False
                        
            # 检查配置文件
            config_file = self.workspace_root / "configs" / f"{self.test_game_id}.yaml"
            if config_file.exists():
                content = config_file.read_text(encoding='utf-8')
                
                required_content = [
                    f"GameID: {self.test_game_id}",
                    "PayoutTable:",
                    "IconWeight:",
                    "WildIcon:",
                    "ScatterIcon:"
                ]
                
                for required in required_content:
                    if required not in content:
                        print(f"配置文件缺少内容: {required}")
                        return False
                        
            print("✓ 文件内容测试通过")
            return True
            
        except Exception as e:
            print(f"文件内容测试出错: {e}")
            return False
            
    def run_all_tests(self) -> bool:
        """运行所有测试"""
        print("开始游戏生成器测试...")
        
        # 设置测试环境
        self.setup_test()
        
        tests = [
            ("基础生成器", self.test_basic_generator),
            ("高级生成器", self.test_advanced_generator),
            ("集成脚本", self.test_integration_script),
            ("文件内容", self.test_file_content)
        ]
        
        passed = 0
        total = len(tests)
        
        for test_name, test_func in tests:
            try:
                if test_func():
                    passed += 1
                else:
                    print(f"✗ {test_name} 测试失败")
            except Exception as e:
                print(f"✗ {test_name} 测试异常: {e}")
                
        # 清理测试文件
        self.cleanup_test()
        
        # 输出结果
        print(f"\n=== 测试结果 ===")
        print(f"通过: {passed}/{total}")
        
        if passed == total:
            print("🎉 所有测试通过！")
            return True
        else:
            print("❌ 部分测试失败！")
            return False


def main():
    """主函数"""
    print("游戏生成器测试脚本")
    print("=" * 40)
    
    # 检查必要文件是否存在
    required_files = [
        "generate_game.py",
        "generate_game_advanced.py", 
        "integrate_game.py"
    ]
    
    missing_files = []
    for file in required_files:
        if not Path(file).exists():
            missing_files.append(file)
            
    if missing_files:
        print(f"缺少必要文件: {', '.join(missing_files)}")
        sys.exit(1)
        
    # 运行测试
    tester = GeneratorTester()
    success = tester.run_all_tests()
    
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
