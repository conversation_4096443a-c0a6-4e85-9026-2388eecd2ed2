#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏引擎框架生成脚本
基于现有的模块结构和模式，自动生成完整的游戏引擎框架

使用方法:
    python generate_game.py <gameId>

示例:
    python generate_game.py 10000999

功能:
- 分析现有模块结构和游戏实现模式
- 生成游戏模块文件 (modules/m{gameId}.go)
- 生成HTTP游戏处理文件 (http/games/s{gameId}.go)
- 生成配置文件模板 (configs/{gameId}.yaml)
- 创建必要的目录结构
- 提供与现有系统集成的接口实现
"""

import os
import sys
import re
import shutil
from pathlib import Path
from typing import Dict, List, Optional
from datetime import datetime


class GameGenerator:
    """游戏代码生成器"""
    
    def __init__(self, game_id: str):
        self.game_id = game_id
        self.workspace_root = Path("/Users/<USER>/Documents/Project/go/src/igame.github.com/igamexDasino")
        self.modules_dir = self.workspace_root / "modules"
        self.http_games_dir = self.workspace_root / "http" / "games"
        self.configs_dir = self.workspace_root / "configs"
        
        # 验证gameId格式
        if not re.match(r'^\d+$', game_id):
            raise ValueError(f"游戏ID必须是数字: {game_id}")
            
        # 确保目录存在
        self.configs_dir.mkdir(exist_ok=True)
        
    def validate_game_id(self) -> bool:
        """验证游戏ID是否已存在"""
        module_file = self.modules_dir / f"m{self.game_id}.go"
        http_file = self.http_games_dir / f"s{self.game_id}.go"
        
        if module_file.exists() or http_file.exists():
            print(f"警告: 游戏ID {self.game_id} 已存在相关文件")
            response = input("是否覆盖现有文件? (y/N): ")
            return response.lower() == 'y'
        return True
        
    def analyze_existing_patterns(self) -> Dict:
        """分析现有的代码模式"""
        patterns = {
            'module_imports': set(),
            'http_imports': set(),
            'common_methods': [],
            'config_fields': set()
        }
        
        # 分析模块文件
        for module_file in self.modules_dir.glob("m*.go"):
            if module_file.name == "example.go":
                continue
            try:
                with open(module_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                    # 提取import语句
                    import_matches = re.findall(r'import \((.*?)\)', content, re.DOTALL)
                    for match in import_matches:
                        for line in match.split('\n'):
                            line = line.strip().strip('"')
                            if line and not line.startswith('//'):
                                patterns['module_imports'].add(line)
            except Exception as e:
                print(f"分析文件 {module_file} 时出错: {e}")
                
        return patterns
        
    def generate_module_file(self) -> str:
        """生成模块文件内容"""
        template = f'''package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m{self.game_id}])

type m{self.game_id} struct {{
	Config c{self.game_id}
	RandByWeight *utils.RandomWeightPicker[int16, int]
}}

type c{self.game_id} struct {{
	GameID      int                 `yaml:"GameID"`      // 游戏ID
	RuleID      int                 `yaml:"RuleID"`      // 生成器规则ID
	Row         int32               `yaml:"Row"`         // 网格行数
	Column      int32               `yaml:"Column"`      // 网格列数
	MaxPayout   int32               `yaml:"MaxPayout"`   // 最大支付
	PayoutTable [][]int32           `yaml:"PayoutTable"` // 支付表
	IconWeight  map[int16]int       `yaml:"IconWeight"`  // 图标权重
	WildIcon    int16               `yaml:"WildIcon"`    // Wild图标ID
	ScatterIcon int16               `yaml:"ScatterIcon"` // Scatter图标ID
}}

func (m m{self.game_id}) ID() int32 {{
	return {self.game_id}
}}

func (m *m{self.game_id}) Line() int32 {{
	return 20 // 默认线数，可根据游戏需求调整
}}

func (m m{self.game_id}) ClientMode() int32 {{
	return basic.EnumClientMode.ONE
}}

func (m m{self.game_id}) Exception(code int32) string {{
	return games.S{self.game_id}{{}}.Exception(code)
}}

func (m *m{self.game_id}) Init(config []byte) {{
	m.Config = utils.ParseYAML[c{self.game_id}](config)
	m.RandByWeight = utils.NewRandomWeightPicker(m.Config.IconWeight)
}}

func (m m{self.game_id}) Rule() string {{
	b, _ := json.Marshal(m.Config)
	return string(b)
}}

func (m m{self.game_id}) InputCoef(ctl int32) int32 {{
	return 100
}}

func (m m{self.game_id}) MinPayout(ctl int32) int32 {{
	return 0
}}

// ZeroSpin 生成一个不中奖的旋转
func (m *m{self.game_id}) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {{
	for {{
		spin := m.Spin(rand.New(rd))
		if spin.Payout() == 0 {{
			return spin
		}}
	}}
}}

// Spin 执行旋转逻辑
func (m *m{self.game_id}) Spin(rd *rand.Rand) basic.ISpin {{
	// TODO: 实现具体的游戏逻辑
	spin := &games.S{self.game_id}{{
		GameId: m.ID(),
		Line:   m.Line(),
		Row:    m.Config.Row,
		Column: m.Config.Column,
		Pages:  []games.P{self.game_id}{{}},
	}}

	// 生成随机网格
	grid := m.generateGrid(rd)

	// 计算中奖
	lines, payout := m.calculateWin(grid)

	page := games.P{self.game_id}{{
		Grid:  grid,
		Lines: lines,
		Pay:   payout,
	}}

	spin.Pages = append(spin.Pages, page)
	spin.Pays = payout

	return spin
}}

// Salting 对旋转结果进行调整
func (m *m{self.game_id}) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {{
	return spin
}}

// generateGrid 生成随机网格
func (m *m{self.game_id}) generateGrid(rd *rand.Rand) basic.Grid {{
	gridSize := int(m.Config.Row * m.Config.Column)
	grid := make(basic.Grid, gridSize)

	for i := 0; i < gridSize; i++ {{
		grid[i] = m.RandByWeight.Pick(rd)
	}}

	return grid
}}

// calculateWin 计算中奖线和奖金
func (m *m{self.game_id}) calculateWin(grid basic.Grid) ([]games.L{self.game_id}, int32) {{
	// TODO: 实现具体的中奖计算逻辑
	lines := []games.L{self.game_id}{{}}
	var totalPayout int32 = 0

	// 这里应该根据具体游戏规则实现中奖计算
	// 示例：简单的连线检查

	return lines, totalPayout
}}
'''
        return template
        
    def generate_http_file(self) -> str:
        """生成HTTP游戏处理文件内容"""
        template = f'''package games

import (
	"encoding/json"
	"igameCommon/basic"
	"time"
)

type S{self.game_id} struct {{
	GameId int32           `json:"gameId"`
	Line   int32           `json:"line"`
	Row    int32           `json:"row"`
	Column int32           `json:"column"`
	Pays   int32           `json:"pays"`
	Pages  []P{self.game_id} `json:"pages"`
}}

type P{self.game_id} struct {{
	Grid  basic.Grid    `json:"grid"`
	Lines []L{self.game_id} `json:"lines"`
	Pay   int32         `json:"pay"`
}}

type L{self.game_id} struct {{
	Icon     int16             `json:"icon"`
	Count    int32             `json:"count"`
	Value    int32             `json:"value"`
	Position []basic.Position  `json:"position"`
}}

func (s S{self.game_id}) Payout() int32 {{
	return s.Pays
}}

func (s S{self.game_id}) RoundPayouts() []int32 {{
	return []int32{{s.Pays}}
}}

func (s S{self.game_id}) Data(ctx basic.SpinContext) string {{
	// 获取上下文参数
	lv := ctx.Int32("lv")
	lineCount := s.Line
	gamble := ctx.Float64("gamble")
	balance := ctx.Float64("balance")
	input := ctx.Float64("input")

	// 扣除投注
	balance -= input
	inputLine := input / float64(lineCount)

	var curReward float64 = 0
	var output float64 = 0

	// 构造返回数据
	result := map[string]interface{}{{
		"gameId":    s.GameId,
		"line":      lineCount,
		"balance":   balance,
		"input":     input,
		"inputLine": inputLine,
		"output":    output,
		"reward":    curReward,
		"gamble":    gamble,
		"lv":        lv,
		"pages":     s.Pages,
		"timestamp": time.Now().Unix(),
	}}

	jsonData, _ := json.Marshal(result)
	return string(jsonData)
}}

func (s S{self.game_id}) RoundData(index int32, ctx basic.SpinContext) string {{
	return s.Data(ctx)
}}

func (s S{self.game_id}) Exception(code int32) string {{
	switch code {{
	case 1001:
		return `{{"error": "insufficient_balance", "message": "余额不足"}}`
	case 1002:
		return `{{"error": "invalid_bet", "message": "投注金额无效"}}`
	case 1003:
		return `{{"error": "game_maintenance", "message": "游戏维护中"}}`
	default:
		return `{{"error": "unknown_error", "message": "未知错误"}}`
	}}
}}

func (s S{self.game_id}) Tag() string {{
	if s.Pays > 0 {{
		return "WIN"
	}}
	return "LOSE"
}}

func (s S{self.game_id}) Tags() []string {{
	tags := []string{{s.Tag()}}

	// 根据奖金大小添加标签
	if s.Pays > 1000 {{
		tags = append(tags, "BIG_WIN")
	}}
	if s.Pays > 5000 {{
		tags = append(tags, "MEGA_WIN")
	}}

	return tags
}}
'''
        return template

    def generate_config_file(self) -> str:
        """生成配置文件内容"""
        config = f'''# 游戏 {self.game_id} 配置文件
# 生成时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

GameID: {self.game_id}
RuleID: 1
Row: 4                    # 网格行数
Column: 5                 # 网格列数
MaxPayout: 250000         # 最大支付

# 支付表配置 [图标ID, 连线数, 倍率]
PayoutTable:
  # 高价值符号
  - [8, 3, 50]    # 3连：50x
  - [8, 4, 200]   # 4连：200x
  - [8, 5, 500]   # 5连：500x

  - [7, 3, 30]    # 3连：30x
  - [7, 4, 120]   # 4连：120x
  - [7, 5, 300]   # 5连：300x

  # 中等价值符号
  - [6, 3, 20]    # 3连：20x
  - [6, 4, 80]    # 4连：80x
  - [6, 5, 200]   # 5连：200x

  - [5, 3, 15]    # 3连：15x
  - [5, 4, 60]    # 4连：60x
  - [5, 5, 150]   # 5连：150x

  # 低价值符号
  - [4, 3, 10]    # 3连：10x
  - [4, 4, 40]    # 4连：40x
  - [4, 5, 100]   # 5连：100x

  - [3, 3, 8]     # 3连：8x
  - [3, 4, 30]    # 4连：30x
  - [3, 5, 80]    # 5连：80x

  - [2, 3, 5]     # 3连：5x
  - [2, 4, 20]    # 4连：20x
  - [2, 5, 50]    # 5连：50x

  - [1, 3, 3]     # 3连：3x
  - [1, 4, 15]    # 4连：15x
  - [1, 5, 30]    # 5连：30x

# 图标权重配置
IconWeight:
  1: 150    # 低价值符号1
  2: 150    # 低价值符号2
  3: 120    # 低价值符号3
  4: 120    # 低价值符号4
  5: 100    # 中等价值符号5
  6: 100    # 中等价值符号6
  7: 80     # 高价值符号7
  8: 80     # 高价值符号8
  9: 30     # Wild符号
  10: 20    # Scatter符号

WildIcon: 9      # Wild图标ID
ScatterIcon: 10  # Scatter图标ID

# 免费旋转配置
FreeSpin:
  Icon: 10       # 触发免费旋转的符号ID
  Number: 3      # 触发免费旋转的最小符号数
  FirstCount: 10 # 首次免费旋转次数
  MoreCount: 2   # 每增加1个符号额外增加的次数

# 特殊功能配置
Special:
  WildMultiplier: 2    # Wild符号倍数
  ScatterPayout: true  # Scatter是否有独立奖励
  CascadeWin: false    # 是否支持级联消除
'''
        return config

    def create_files(self) -> bool:
        """创建所有文件"""
        try:
            # 生成模块文件
            module_content = self.generate_module_file()
            module_file = self.modules_dir / f"m{self.game_id}.go"
            with open(module_file, 'w', encoding='utf-8') as f:
                f.write(module_content)
            print(f"✓ 已生成模块文件: {module_file}")

            # 生成HTTP文件
            http_content = self.generate_http_file()
            http_file = self.http_games_dir / f"s{self.game_id}.go"
            with open(http_file, 'w', encoding='utf-8') as f:
                f.write(http_content)
            print(f"✓ 已生成HTTP文件: {http_file}")

            # 生成配置文件
            config_content = self.generate_config_file()
            config_file = self.configs_dir / f"{self.game_id}.yaml"
            with open(config_file, 'w', encoding='utf-8') as f:
                f.write(config_content)
            print(f"✓ 已生成配置文件: {config_file}")

            return True

        except Exception as e:
            print(f"✗ 创建文件时出错: {e}")
            return False

    def generate_usage_instructions(self) -> str:
        """生成使用说明"""
        instructions = f"""
========================================
游戏 {self.game_id} 框架生成完成！
========================================

已生成的文件:
1. modules/m{self.game_id}.go     - 游戏模块文件
2. http/games/s{self.game_id}.go  - HTTP游戏处理文件
3. configs/{self.game_id}.yaml    - 配置文件

下一步操作:
1. 根据具体游戏需求修改配置文件 configs/{self.game_id}.yaml
2. 实现游戏逻辑:
   - 在 m{self.game_id}.Spin() 方法中实现旋转逻辑
   - 在 m{self.game_id}.calculateWin() 方法中实现中奖计算
   - 根据需要添加特殊功能（免费旋转、级联等）

3. 生成游戏数据:
   cd bin && go run ../cmd/generate/main.go {self.game_id}

4. 生成支付表:
   cd bin && go run ../cmd/paytab/main.go {self.game_id} 0

5. 测试游戏:
   go test -bench=BenchmarkSpin -v ./test -args {self.game_id}

注意事项:
- 所有TODO注释的地方需要根据具体游戏规则实现
- 配置文件中的支付表和权重需要根据游戏设计调整
- 建议先实现基础功能，再逐步添加特殊功能
- 确保遵循现有的代码规范和接口定义

========================================
"""
        return instructions

    def run(self) -> bool:
        """运行生成器"""
        print(f"开始生成游戏 {self.game_id} 的框架...")

        # 验证游戏ID
        if not self.validate_game_id():
            print("操作已取消")
            return False

        # 分析现有模式
        print("分析现有代码模式...")
        patterns = self.analyze_existing_patterns()
        print(f"发现 {len(patterns['module_imports'])} 个模块导入模式")

        # 创建文件
        print("生成文件...")
        if not self.create_files():
            return False

        # 显示使用说明
        print(self.generate_usage_instructions())

        return True


def main():
    """主函数"""
    if len(sys.argv) != 2:
        print("使用方法: python generate_game.py <gameId>")
        print("示例: python generate_game.py 10000999")
        sys.exit(1)

    game_id = sys.argv[1]

    try:
        generator = GameGenerator(game_id)
        success = generator.run()

        if success:
            print("\\n🎉 游戏框架生成成功！")
            sys.exit(0)
        else:
            print("\\n❌ 游戏框架生成失败！")
            sys.exit(1)

    except Exception as e:
        print(f"\\n❌ 发生错误: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
