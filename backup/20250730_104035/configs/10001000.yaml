# 游戏 10001000 配置文件
# 游戏类型: ways (Ways玩法)

Row: 4
Column: 6
MaxPayout: 250000

# Wild图标配置
WildIcon: 9

# 赔率表配置 (格式: [符号ID, 连线数, 赔率*100])
PayoutTable:
  # 发 (8)
  - [8, 3, 15]    # 3连：15x
  - [8, 4, 60]    # 4连：60x
  - [8, 5, 100]   # 5连：100x
  # 中 (7)
  - [7, 3, 10]    # 3连：10x
  - [7, 4, 40]    # 4连：40x
  - [7, 5, 80]    # 5连：80x
  # 白板 (6)
  - [6, 3, 8]     # 3连：8x
  - [6, 4, 20]    # 4连：20x
  - [6, 5, 60]    # 5连：60x
  # 八万 (5)
  - [5, 3, 6]     # 3连：6x
  - [5, 4, 15]    # 4连：15x
  - [5, 5, 40]    # 5连：40x
  # 五筒 (4)
  - [4, 3, 4]     # 3连：4x
  - [4, 4, 10]    # 4连：10x
  - [4, 5, 20]    # 5连：20x
  # 五条 (3)
  - [3, 3, 4]     # 3连：4x
  - [3, 4, 10]    # 4连：10x
  - [3, 5, 20]    # 5连：20x
  # 二筒 (2)
  - [2, 3, 2]     # 3连：2x
  - [2, 4, 5]     # 4连：5x
  - [2, 5, 10]    # 5连：10x
  # 二条 (1)
  - [1, 3, 2]     # 3连：2x
  - [1, 4, 5]     # 4连：5x
  - [1, 5, 10]    # 5连：10x

# 基础级联乘数 (普通模式)
BaseCoef:
  - 1   # 第一次 cascading：乘数为1
  - 2   # 第二次 cascading：乘数从 x1 提升至 x2
  - 3   # 第三次 cascading：乘数从 x2 提升至 x3
  - 5   # 第四次 cascading：乘数从 x3 提升至 x5

# 免费旋转级联乘数
FreeCoef:
  - 2   # 第一次 cascading：乘数从 x1 提升至 x2
  - 4   # 第二次 cascading：乘数从 x2 提升至 x4
  - 6   # 第三次 cascading：乘数从 x4 提升至 x6
  - 10  # 第四次 cascading：乘数从 x6 提升至 x10

# 图标权重配置
IconWeight:
  1: 100  # 二条
  2: 100  # 二筒
  3: 100  # 五条
  4: 100  # 五筒
  5: 100  # 八万
  6: 100  # 白板
  7: 100  # 中
  8: 100  # 发
  9: 0    # Wild (不直接出现)
  10: 30  # 胡 (Scatter)

# 免费旋转配置
FreeSpin:
  Icon: 10        # 胡作为 Scatter 触发免费旋转
  Number: 3       # 3个 Scatter 触发免费旋转
  FirstCount: 12  # 3个Scatter触发旋转次数为 12 次
  MoreCount: 2    # 每增加1个Scatter增加2次免费旋转
