# Makefile

PHONY: gen test pprof

gen: 
	@cd bin & go run cmd/generate/main.go $(id)

pprof:
	@go tool pprof -http=:8080 logs/cpu.pprof

test: force
	@go test -bench=BenchmarkSpin -timeout 10s -v ./test -args $(id)

testtax: force
	@go test -bench=BenchmarkTax -timeout 10000s -v ./test -args $(id) $(ctl) $(tax)

force:  # 强制执行
	@true

build:
	GOOS=windows GOARCH=amd64 go build -tags exe -o bin/generate.exe cmd/generate/main.go
	GOOS=windows GOARCH=amd64 go build -tags exe -o bin/paytab.exe cmd/paytab/main.go

request:
	@curl -X POST "localhost:9528" -H 'Content-Type: application/json' -d '{"GameID": $(id), "SpinID": $(spin)}'

paytab:
	@cd bin & go run ../cmd/paytab/main.go $(id) $(ctl)

simulate:
	@cd bin & go run ../cmd/simulate/main.go $(id) $(ctl) $(tax) $(n)

service:
	@cd bin & go run cmd/service/main.go $(id)

# 游戏引擎框架生成器相关命令
# ================================

## gamegen-help: 显示gamegen帮助信息
gamegen-help:
	@echo "🎮 游戏引擎框架生成器命令"
	@echo "=========================="
	@echo "make gamegen-build                    # 构建gamegen"
	@echo "make gamegen-test                     # 测试gamegen"
	@echo "make gamegen-demo                     # 运行演示"
	@echo "make gamegen-slot id=10000999         # 生成slot游戏"
	@echo "make gamegen-ways id=10001000         # 生成ways游戏"
	@echo "make gamegen-cascade id=10001001      # 生成cascade游戏"
	@echo "make gamegen-line id=10001002         # 生成line游戏"

## gamegen-build: 构建gamegen工具
gamegen-build:
	@echo "🔨 构建gamegen..."
	@cd cmd/gamegen && go build -o ../../gamegen .
	@echo "✅ gamegen构建完成"

## gamegen-test: 测试gamegen工具
gamegen-test: gamegen-build
	@echo "🧪 测试gamegen..."
	@./scripts/test-gamegen.sh
	@echo "✅ gamegen测试完成"

## gamegen-demo: 运行gamegen演示
gamegen-demo: gamegen-build
	@echo "🎬 运行gamegen演示..."
	@./gamegen -id=10000999 -type=slot -force
	@./gamegen -id=10001000 -type=ways -row=4 -column=6 -force
	@./gamegen -id=10001001 -type=cascade -row=6 -column=6 -max-payout=1000000 -force
	@./gamegen -id=10001002 -type=line -force
	@echo "🎉 演示完成！"

## gamegen-slot: 生成slot游戏
gamegen-slot: gamegen-build
	@if [ -z "$(id)" ]; then \
		echo "❌ 请指定游戏ID: make gamegen-slot id=10000999"; \
		exit 1; \
	fi
	@./gamegen -id=$(id) -type=slot -force

## gamegen-ways: 生成ways游戏
gamegen-ways: gamegen-build
	@if [ -z "$(id)" ]; then \
		echo "❌ 请指定游戏ID: make gamegen-ways id=10001000"; \
		exit 1; \
	fi
	@./gamegen -id=$(id) -type=ways -row=4 -column=6 -force

## gamegen-cascade: 生成cascade游戏
gamegen-cascade: gamegen-build
	@if [ -z "$(id)" ]; then \
		echo "❌ 请指定游戏ID: make gamegen-cascade id=10001001"; \
		exit 1; \
	fi
	@./gamegen -id=$(id) -type=cascade -row=6 -column=6 -max-payout=1000000 -force

## gamegen-line: 生成line游戏
gamegen-line: gamegen-build
	@if [ -z "$(id)" ]; then \
		echo "❌ 请指定游戏ID: make gamegen-line id=10001002"; \
		exit 1; \
	fi
	@./gamegen -id=$(id) -type=line -force

## gamegen-interactive: 交互式生成游戏
gamegen-interactive: gamegen-build
	@if [ -z "$(id)" ]; then \
		echo "❌ 请指定游戏ID: make gamegen-interactive id=10000999"; \
		exit 1; \
	fi
	@./gamegen -id=$(id) -interactive

## gamegen-clean: 清理gamegen相关文件
gamegen-clean:
	@echo "🧹 清理gamegen文件..."
	@rm -f gamegen
	@rm -rf build/
	@rm -rf backup/
	@echo "✅ 清理完成"