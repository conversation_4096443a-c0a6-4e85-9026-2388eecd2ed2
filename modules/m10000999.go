package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m10000999])

type m10000999 struct {
	Config c10000999
}

type c10000999 struct {
	GameID      int                    // 游戏ID
	RuleID      int                    // 生成器规则ID
	Row         int32                  // 网格行数
	Column      int32                  // 网格列数
	MaxPayout   int32                  // 最大支付
	WildIcon    int16                  // Wild图标
	ScatterIcon int16                  // Scatter图标
	PayoutTable map[int16][]int16      // 支付表
	IconWeight  map[int16]int          // 图标权重
	FreeSpin    FreeSpinConfig10000999 // 免费旋转配置
}

type FreeSpinConfig10000999 struct {
	Icon      int16 // 触发免费旋转的符号ID
	MinCount  int   // 触发免费旋转的最小符号数
	FreeCount int   // 免费旋转次数
}

func (m m10000999) ID() int32 {
	return 10000999
}

func (m m10000999) Line() int32 {
	return 20
}

func (m m10000999) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10000999) Exception(code int32) string {
	return games.S10000999{}.Exception(code)
}

func (m *m10000999) Init(config []byte) {
	m.Config = utils.ParseYAML[c10000999](config)
}

func (m *m10000999) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m10000999) Spin(rd *rand.Rand) basic.ISpin {
	// TODO: 实现游戏逻辑
	return games.S10000999{
		Pays: 0,
	}
}

func (m *m10000999) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	return spin
}

func (m m10000999) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m10000999) InputCoef(ctl int32) int32 {
	return 100
}

func (m m10000999) MinPayout(ctl int32) int32 {
	return 0
}
