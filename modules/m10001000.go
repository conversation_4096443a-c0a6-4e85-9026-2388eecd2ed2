package modules

import (
	"encoding/json"
	"igameCommon/basic"
	"igameCommon/utils"
	"igameHttp/games"
	"math/rand"
)

var _ = Factory.reg(basic.NewGeneral[*m10001000])

type m10001000 struct {
	Config c10001000
}

type c10001000 struct {
	Row         int32                  // 行数
	Column      int32                  // 列数
	WildIcon    int32                  // Wild符号ID
	PayoutTable [][3]int32             // 赔率表 [符号ID, 连线数, 赔率*100]
	FreeSpin    FreeSpinConfig10001000 // 免费旋转配置
	BaseCoef    []int32                // 普通模式级联乘数
	FreeCoef    []int32                // 免费旋转模式级联乘数
	MaxPayout   int32                  // 最大派彩
	IconWeight  map[int32]int32        // 符号权重
}

type FreeSpinConfig10001000 struct {
	Icon       int32 // 触发免费旋转的符号ID
	Number     int   // 触发免费旋转的最小符号数
	FirstCount int   // 首次免费旋转次数
	MoreCount  int   // 额外触发免费旋转的次数
}

func (m *m10001000) ID() int32 {
	return 10001000
}

func (m *m10001000) Line() int32 {
	return 20
}

func (m m10001000) ClientMode() int32 {
	return basic.EnumClientMode.ONE
}

func (m m10001000) Exception(code int32) string {
	return (&games.S10001000{}).Exception(code)
}

func (m *m10001000) Init(config []byte) {
	m.Config = utils.ParseYAML[c10001000](config)
}

func (m *m10001000) ZeroSpin(ctl int32, rd *rand.Rand) basic.ISpin {
	for {
		spin := m.Spin(rd)
		if spin.Payout() == 0 {
			return spin
		}
	}
}

func (m *m10001000) Spin(rd *rand.Rand) basic.ISpin {
	// TODO: 实现Ways玩法逻辑
	return &games.S10001000{
		Pays: 0,
	}
}

func (m *m10001000) Salting(spin basic.ISpin, salt *rand.Rand) basic.ISpin {
	return spin
}

func (m m10001000) Rule() string {
	b, _ := json.Marshal(m.Config)
	return string(b)
}

func (m m10001000) InputCoef(ctl int32) int32 {
	return 100
}

func (m m10001000) MinPayout(ctl int32) int32 {
	return 0
}
